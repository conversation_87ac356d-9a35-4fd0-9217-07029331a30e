document.addEventListener("DOMContentLoaded", function () {
  const apiKeyInput = document.getElementById("apiKey");
  const saveKeyBtn = document.getElementById("saveKey");
  const answerQuizBtn = document.getElementById("answerQuiz");
  const submitQuizBtn = document.getElementById("submitQuiz");
  const status = document.getElementById("status");

  // Load saved API key
  chrome.storage.sync.get(["openaiApiKey"], function (result) {
    if (result.openaiApiKey) {
      apiKeyInput.value = result.openaiApiKey;
    }
  });

  saveKeyBtn.addEventListener("click", function () {
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
      chrome.storage.sync.set({ openaiApiKey: apiKey }, function () {
        status.textContent = "API Key saved!";
        setTimeout(() => (status.textContent = ""), 2000);
      });
    }
  });

  answerQuizBtn.addEventListener("click", function () {
    status.textContent = "Starting quiz...";

    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      console.log("Active tab:", tabs[0]);

      chrome.tabs.sendMessage(
        tabs[0].id,
        { action: "answerQuiz" },
        function (response) {
          if (chrome.runtime.lastError) {
            const errorMsg = chrome.runtime.lastError.message;
            console.error("Extension connection error:", errorMsg);

            if (errorMsg.includes("Receiving end does not exist")) {
              status.textContent =
                "Error: Content script not loaded. Please refresh the page and try again.";
            } else if (errorMsg.includes("Cannot access")) {
              status.textContent =
                "Error: Cannot access this page. Try a different page.";
            } else {
              status.textContent = `Error: ${errorMsg}`;
            }
            return;
          }

          console.log("Response received:", response);

          if (response && response.success) {
            status.textContent = `Quiz answered! (${response.answeredCount} questions)`;
          } else {
            status.textContent =
              "Error: " + (response?.error || "Unknown error");
          }
        }
      );
    });
  });

  submitQuizBtn.addEventListener("click", function () {
    status.textContent = "Starting automated quiz submission...";

    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      console.log("Active tab:", tabs[0]);

      chrome.tabs.sendMessage(
        tabs[0].id,
        { action: "submitQuizWithRetry" },
        function (response) {
          if (chrome.runtime.lastError) {
            const errorMsg = chrome.runtime.lastError.message;
            console.error("Extension connection error:", errorMsg);

            if (errorMsg.includes("Receiving end does not exist")) {
              status.textContent =
                "Error: Content script not loaded. Please refresh the page and try again.";
            } else if (errorMsg.includes("Cannot access")) {
              status.textContent =
                "Error: Cannot access this page. Try a different page.";
            } else {
              status.textContent = `Error: ${errorMsg}`;
            }
            return;
          }

          console.log("Submission response received:", response);

          if (response && response.success) {
            status.textContent = `Perfect score achieved! Score: ${response.score}/30 in ${response.attempts} attempt(s) 🎉`;
          } else {
            status.textContent =
              "Error: " + (response?.error || "Unknown error");
          }
        }
      );
    });
  });
});
