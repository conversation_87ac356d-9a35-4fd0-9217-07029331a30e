// Background script to handle API calls and bypass CORS restrictions
console.log("SOTATEK Quiz Extension: Background script loaded");

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("Background script received message:", request);

  if (request.action === "submitQuizAPI") {
    handleQuizSubmission(request.data)
      .then(result => {
        console.log("Background script: Quiz submission successful:", result);
        sendResponse({ success: true, data: result });
      })
      .catch(error => {
        console.error("Background script: Quiz submission failed:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }

  if (request.action === "makeOpenAIRequest") {
    handleOpenAIRequest(request.data)
      .then(result => {
        console.log("Background script: OpenAI request successful");
        sendResponse({ success: true, data: result });
      })
      .catch(error => {
        console.error("Background script: OpenAI request failed:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }
});

/**
 * Handle quiz submission to SotaTek API
 */
async function handleQuizSubmission(requestData) {
  const { quizHistoryId, answers, authToken, retryCount = 0 } = requestData;
  
  try {
    const requestBody = {
      quizHistoryId: quizHistoryId,
      answers: answers
    };
    
    console.log("Background script: Submitting to SotaTek API:", requestBody);
    
    const response = await fetch("https://api.lms.sotatek.com/quiz", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "authorization": `Bearer ${authToken}`,
        "content-type": "application/json",
        "origin": "https://lms.sotatek.com",
        "referer": "https://lms.sotatek.com/"
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      // Handle rate limiting with exponential backoff
      if (response.status === 429 && retryCount < 3) {
        const waitTime = Math.pow(2, retryCount) * 5000; // 5s, 10s, 20s
        console.log(`Background script: Rate limited (429), waiting ${waitTime}ms before retry ${retryCount + 1}/3...`);
        
        await new Promise(resolve => setTimeout(resolve, waitTime));
        
        // Retry with incremented count
        return handleQuizSubmission({
          quizHistoryId,
          answers,
          authToken,
          retryCount: retryCount + 1
        });
      }
      
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    console.log("Background script: SotaTek API response:", result);
    
    return result;
    
  } catch (error) {
    console.error("Background script: SotaTek API submission error:", error);
    throw error;
  }
}

/**
 * Handle OpenAI API requests
 */
async function handleOpenAIRequest(requestData) {
  const { messages, apiKey, model = "gpt-4o-mini", temperature = 0, maxTokens = 500 } = requestData;
  
  try {
    console.log("Background script: Making OpenAI API request");
    
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        temperature: temperature,
        max_tokens: maxTokens
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || "Unknown error"}`);
    }
    
    const result = await response.json();
    console.log("Background script: OpenAI API response received");
    
    return result;
    
  } catch (error) {
    console.error("Background script: OpenAI API error:", error);
    throw error;
  }
}

/**
 * Handle installation and updates
 */
chrome.runtime.onInstalled.addListener((details) => {
  console.log("SOTATEK Quiz Extension: Extension installed/updated", details);
  
  if (details.reason === 'install') {
    console.log("SOTATEK Quiz Extension: First time installation");
  } else if (details.reason === 'update') {
    console.log("SOTATEK Quiz Extension: Extension updated");
  }
});

/**
 * Handle extension startup
 */
chrome.runtime.onStartup.addListener(() => {
  console.log("SOTATEK Quiz Extension: Extension started");
});

/**
 * Keep service worker alive
 */
chrome.runtime.onConnect.addListener((port) => {
  console.log("Background script: Port connected");
  
  port.onDisconnect.addListener(() => {
    console.log("Background script: Port disconnected");
  });
});

// Periodic keepalive to prevent service worker from sleeping
setInterval(() => {
  console.log("Background script: Keepalive ping");
}, 25000); // Every 25 seconds
