# Troubleshooting Guide

## CORS Error Resolution

### Problem
You're encountering CORS (Cross-Origin Resource Sharing) errors when the extension tries to make API calls to the SotaTek LMS.

### Root Cause
Browser security policies prevent web pages from making requests to different domains unless explicitly allowed. Chrome extensions have special permissions to bypass these restrictions.

### Solution Implemented

#### 1. Updated Manifest Permissions
Added `host_permissions` to allow API access:
```json
{
  "host_permissions": [
    "https://api.lms.sotatek.com/*",
    "https://lms.sotatek.com/*",
    "https://api.openai.com/*"
  ]
}
```

#### 2. Background Script Architecture
Created `background.js` to handle API calls:
- Background scripts run in a privileged context
- Can make cross-origin requests without CORS restrictions
- Acts as a proxy between content script and external APIs

#### 3. Message Passing System
Content script → Background script → External API:
```javascript
// Content script sends message
chrome.runtime.sendMessage({
  action: "submitQuizAPI",
  data: { quizHistoryId, answers, authToken }
});

// Background script handles API call
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "submitQuizAPI") {
    handleQuizSubmission(request.data)
      .then(result => sendResponse({ success: true, data: result }));
  }
});
```

### Installation Steps

1. **Reload Extension**:
   - Go to `chrome://extensions/`
   - Find "Quiz Auto Answer" extension
   - Click the reload button (🔄)

2. **Verify Permissions**:
   - Check that the extension shows permissions for:
     - `api.lms.sotatek.com`
     - `lms.sotatek.com`
     - `api.openai.com`

3. **Test Background Script**:
   - Open Developer Tools (F12)
   - Go to Extensions tab → Service Workers
   - Click "Inspect" next to the extension
   - Check for background script logs

## Common Issues and Solutions

### 1. "Content script not loaded"

**Symptoms**: Extension popup shows "Content script not loaded" error

**Solutions**:
- Refresh the quiz page
- Ensure you're on a valid SotaTek LMS quiz page
- Check that the extension is enabled
- Reload the extension in `chrome://extensions/`

### 2. "Could not extract quiz data"

**Symptoms**: Extension can't find quiz information on the page

**Solutions**:
- Ensure the quiz is fully loaded
- Check that questions are visible on the page
- Try scrolling down to load all questions
- Verify you're on the correct quiz page format

### 3. "Could not extract authentication token"

**Symptoms**: Extension can't find authentication credentials

**Solutions**:
- Log in to SotaTek LMS first
- Refresh the page after logging in
- Check browser storage for auth tokens:
  - Open DevTools → Application → Storage
  - Look for tokens in localStorage/sessionStorage
- The system will use a fallback token if needed

### 4. "Background script API call failed"

**Symptoms**: API calls fail even with background script

**Solutions**:
- Check network connectivity
- Verify SotaTek LMS is accessible
- Ensure authentication token is valid
- Check background script console for detailed errors

### 5. Rate Limiting (429 Errors)

**Symptoms**: "Rate limited" messages in console

**Solutions**:
- The system automatically handles rate limiting
- Wait times increase exponentially: 5s, 10s, 20s
- If persistent, wait longer between attempts
- Check if multiple instances are running

## Debug Steps

### 1. Check Extension Status
```javascript
// In browser console on quiz page
console.log("Extension loaded:", typeof extractQuizData === 'function');
```

### 2. Test Data Extraction
```javascript
// Test quiz data extraction
const quizData = extractQuizData();
console.log("Quiz data:", quizData);

// Test auth token extraction
const authToken = extractAuthToken();
console.log("Auth token length:", authToken?.length);
```

### 3. Monitor Background Script
1. Go to `chrome://extensions/`
2. Find your extension
3. Click "Inspect views: service worker"
4. Check console for background script logs

### 4. Network Monitoring
1. Open DevTools → Network tab
2. Filter by "api.lms.sotatek.com"
3. Watch for API requests and responses
4. Check request headers and response codes

## Advanced Troubleshooting

### Manual API Testing
Test the API directly in DevTools console:
```javascript
fetch("https://api.lms.sotatek.com/quiz", {
  method: "POST",
  headers: {
    "authorization": "Bearer YOUR_TOKEN_HERE",
    "content-type": "application/json"
  },
  body: JSON.stringify({
    quizHistoryId: 18428,
    answers: [{"questionId": 849, "answers": [0]}]
  })
})
.then(r => r.json())
.then(console.log)
.catch(console.error);
```

### Extension Permissions Check
```javascript
// Check if extension has required permissions
chrome.permissions.contains({
  origins: ["https://api.lms.sotatek.com/*"]
}, (result) => {
  console.log("Has API permission:", result);
});
```

### Background Script Communication Test
```javascript
// Test background script communication
chrome.runtime.sendMessage({
  action: "test"
}, (response) => {
  console.log("Background script response:", response);
});
```

## Performance Optimization

### 1. Reduce API Calls
- Batch multiple questions when possible
- Cache successful answer patterns
- Use systematic approach for unknown errors

### 2. Optimize Wait Times
- Start with shorter waits for first attempts
- Increase exponentially for rate limiting
- Maximum wait time of 10 seconds

### 3. Memory Management
- Clear large data structures after use
- Avoid storing sensitive data
- Use efficient data structures

## Security Considerations

### 1. Token Handling
- Tokens are extracted dynamically
- No permanent storage of credentials
- Fallback token for development only

### 2. API Usage
- Respects rate limiting
- Uses proper authentication headers
- Maintains session integrity

### 3. Data Privacy
- No quiz data stored permanently
- Logging only to browser console
- No external data transmission except APIs

## Getting Help

### 1. Check Console Logs
Always check browser console for detailed error messages:
- Content script logs: Main page console
- Background script logs: Extension service worker console
- Popup logs: Extension popup console

### 2. Verify Prerequisites
- Valid SotaTek LMS account
- Active quiz session
- Extension properly installed and enabled
- Required permissions granted

### 3. Test Environment
- Try in incognito mode
- Disable other extensions temporarily
- Clear browser cache and cookies
- Test with fresh browser session

The system is designed to be robust and self-recovering, but these troubleshooting steps should help resolve any issues that arise.
