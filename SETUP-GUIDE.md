# Setup Guide - Automated Quiz Submission System

## Quick Start

### 1. Install the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the folder containing the extension files
5. The extension should appear in your extensions list

### 2. Configure OpenAI API Key
1. Get an OpenAI API key from https://platform.openai.com/api-keys
2. Click the extension icon in Chrome toolbar
3. Enter your API key in the password field
4. Click "Save API Key"

### 3. Navigate to Quiz
1. Log in to SotaTek LMS (https://lms.sotatek.com/)
2. Navigate to any quiz page
3. Ensure the quiz is loaded and questions are visible

### 4. Run Automated Submission
1. Click the extension icon
2. Click "Submit Quiz with Retry"
3. Wait for the system to achieve 30/30 score
4. Monitor progress in the extension popup

## Detailed Setup

### Extension Files Required
Make sure you have all these files in your extension folder:
```
├── manifest.json          # Extension configuration
├── content.js             # Main quiz logic
├── background.js          # API handler (CORS bypass)
├── popup.html             # Extension interface
├── popup.js               # Popup functionality
├── iso27001-reference.js  # Security reference data
└── README files           # Documentation
```

### Permissions Verification
After installation, verify the extension has these permissions:
- **Active Tab**: To interact with quiz pages
- **Storage**: To save API key
- **Host Permissions**:
  - `https://api.lms.sotatek.com/*` (SotaTek API)
  - `https://lms.sotatek.com/*` (SotaTek LMS)
  - `https://api.openai.com/*` (OpenAI API)

### Testing the Installation

#### 1. Basic Functionality Test
1. Go to any SotaTek quiz page
2. Open browser console (F12)
3. Type: `console.log(typeof extractQuizData)`
4. Should return: `"function"`

#### 2. Background Script Test
1. Go to `chrome://extensions/`
2. Find your extension
3. Click "Inspect views: service worker"
4. Should see background script console with logs

#### 3. API Key Test
1. Click extension icon
2. Enter a test API key
3. Click "Save API Key"
4. Should see "API Key saved!" message

## Usage Instructions

### Standard Quiz Answering
1. **Load Quiz Page**: Navigate to any SotaTek LMS quiz
2. **Click Extension**: Click the extension icon in toolbar
3. **Answer Quiz**: Click "Answer Quiz" button
4. **Wait**: System will analyze and answer all questions
5. **Review**: Check answers before submitting manually

### Automated Submission (New Feature)
1. **Load Quiz Page**: Navigate to any SotaTek LMS quiz
2. **Click Extension**: Click the extension icon in toolbar
3. **Start Automation**: Click "Submit Quiz with Retry"
4. **Monitor Progress**: Watch the status messages
5. **Wait for Completion**: System will retry until 30/30 achieved

### Status Messages
- `"Starting automated quiz submission..."` - System initializing
- `"Attempt X/10"` - Current submission attempt
- `"Found X incorrect answers, updating..."` - Analyzing and correcting
- `"Perfect score achieved! Score: 30/30 in X attempt(s) 🎉"` - Success!

## Configuration Options

### API Key Management
- **Storage**: API key is stored securely in Chrome storage
- **Privacy**: Key is only used for OpenAI API calls
- **Backup**: Save your API key separately for safety

### Retry Settings (Advanced)
Default settings in `content.js`:
```javascript
const maxAttempts = 10;        // Maximum submission attempts
const baseWaitTime = 2000;     // Base wait time (2 seconds)
const maxWaitTime = 10000;     // Maximum wait time (10 seconds)
```

### Question ID Mapping (Advanced)
The system auto-detects question IDs, but you can customize:
```javascript
// In extractQuizData() function
questionId = 833 + index; // Adjust base ID as needed
```

## Troubleshooting Quick Fixes

### Extension Not Working
1. **Reload Extension**: Go to `chrome://extensions/` and click reload
2. **Refresh Page**: Refresh the quiz page
3. **Check Console**: Look for error messages in browser console

### CORS Errors
1. **Verify Permissions**: Check extension has host permissions
2. **Background Script**: Ensure background.js is loaded
3. **Reload Extension**: Force reload the extension

### Authentication Issues
1. **Login First**: Ensure you're logged in to SotaTek LMS
2. **Refresh Token**: Refresh the page to get fresh auth token
3. **Check Storage**: Verify auth token in browser storage

### API Rate Limiting
1. **Wait**: System automatically handles rate limiting
2. **Patience**: Each retry waits longer (exponential backoff)
3. **Monitor**: Check console for rate limit messages

## Best Practices

### 1. Preparation
- Always log in to SotaTek LMS first
- Ensure stable internet connection
- Close unnecessary browser tabs
- Have OpenAI API key ready

### 2. During Use
- Don't navigate away from quiz page
- Keep extension popup open to monitor progress
- Don't click browser back/forward buttons
- Let the system complete all attempts

### 3. After Use
- Review the final score
- Check console logs if issues occurred
- Save successful patterns for future reference

## Advanced Features

### 1. Multiple Answer Strategies
- **AI-Powered**: Uses OpenAI to analyze incorrect questions
- **Systematic**: Cycles through answer options methodically
- **Random**: Last resort fallback strategy

### 2. Intelligent Retry Logic
- **Exponential Backoff**: Increasing wait times for rate limits
- **Error Recovery**: Automatic fallback to different strategies
- **Progress Tracking**: Detailed logging of each attempt

### 3. Data Extraction
- **Auto-Detection**: Finds quiz IDs and question data automatically
- **Multiple Methods**: Tries various extraction techniques
- **Fallback Values**: Uses defaults when auto-detection fails

## Support and Maintenance

### Regular Updates
- Keep OpenAI API key active and funded
- Update extension when new versions available
- Monitor SotaTek LMS for interface changes

### Performance Monitoring
- Check success rates in console logs
- Monitor API usage and costs
- Optimize settings based on results

### Security
- Keep API key secure and private
- Don't share extension with unauthorized users
- Monitor for any suspicious activity

## Getting Help

### 1. Check Documentation
- Read `TROUBLESHOOTING.md` for common issues
- Review `AUTOMATED-QUIZ-SUBMISSION.md` for technical details
- Check console logs for specific error messages

### 2. Debug Steps
1. Open browser console (F12)
2. Look for error messages
3. Check network tab for failed requests
4. Verify extension permissions

### 3. Common Solutions
- Reload extension
- Refresh quiz page
- Clear browser cache
- Try incognito mode

The system is designed to be user-friendly and robust, handling most issues automatically while providing clear feedback about its progress.
