# Automated Quiz Submission System

## Overview

This system provides automated quiz submission with retry logic to achieve a perfect score of 30/30 questions on the SotaTek LMS platform. The system intelligently submits quiz answers, analyzes responses, and automatically retries with corrected answers until achieving the target score.

## Features

### 🎯 Core Functionality
- **Automated API Submission**: Submits quiz answers via the SotaTek LMS API
- **Response Analysis**: Parses API responses to identify incorrect answers
- **Intelligent Retry Logic**: Continues submitting until achieving 30/30 score
- **Rate Limiting Protection**: Handles API rate limits with exponential backoff
- **Multiple Fallback Strategies**: AI-powered, systematic, and random answer updates

### 🔧 Technical Features
- **Authentication Handling**: Automatically extracts and uses authentication tokens
- **Quiz Data Extraction**: Intelligently extracts quiz IDs and question data from the page
- **Error Recovery**: Robust error handling with multiple retry strategies
- **Progress Logging**: Comprehensive logging for debugging and progress tracking

## How It Works

### 1. Data Extraction
The system automatically extracts:
- **Quiz History ID**: From URL parameters, page data, or fallback values
- **Question IDs**: From DOM attributes, question text patterns, or generated sequences
- **Current Answers**: From selected checkboxes and radio buttons
- **Authentication Token**: From localStorage, sessionStorage, cookies, or fallback

### 2. API Submission
```javascript
POST https://api.lms.sotatek.com/quiz
{
  "quizHistoryId": 18428,
  "answers": [
    {"questionId": 849, "answers": [2]},
    {"questionId": 861, "answers": [0]},
    // ... more answers
  ]
}
```

### 3. Response Analysis
The system handles multiple API response formats:
- **Detailed Results**: Array with per-question correctness
- **Incorrect Question List**: Direct list of wrong question IDs
- **Score Only**: Total score requiring systematic approach

### 4. Answer Update Strategies

#### AI-Powered Updates (Primary)
- Uses OpenAI API to re-analyze incorrect questions
- Leverages SOTATEK ISMS knowledge base
- Provides most accurate corrections

#### Systematic Updates (Fallback)
- Cycles through answer options systematically
- Used when specific incorrect questions aren't identified
- Ensures all possibilities are tried

#### Random Updates (Last Resort)
- Randomly selects different answers for incorrect questions
- Used when other strategies fail
- Prevents infinite loops

## Usage

### Basic Usage
1. Navigate to a SotaTek LMS quiz page
2. Click the extension icon
3. Click "Submit Quiz with Retry"
4. Wait for the system to achieve 30/30 score

### Advanced Configuration
The system automatically handles most configurations, but you can customize:
- **Max Attempts**: Default 10 attempts (configurable in code)
- **Wait Times**: 2-10 seconds between attempts with exponential backoff
- **Fallback Token**: Hardcoded token for authentication fallback

## API Integration

### Request Structure
```javascript
{
  method: "POST",
  headers: {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "authorization": "Bearer [TOKEN]",
    "content-type": "application/json",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site"
  },
  body: JSON.stringify({
    quizHistoryId: number,
    answers: [
      {questionId: number, answers: [number]}
    ]
  })
}
```

### Response Handling
The system handles various response formats:
- Success with score/correctAnswers count
- Detailed results array
- Error responses with retry logic

## Error Handling

### Rate Limiting (429 Errors)
- Exponential backoff: 5s, 10s, 20s
- Automatic retry up to 3 times per submission
- Longer waits between submission attempts

### Authentication Errors
- Multiple token extraction methods
- Fallback to hardcoded token
- Clear error messages for token issues

### Network Errors
- Retry logic for temporary failures
- Graceful degradation to fallback strategies
- Comprehensive error logging

## Logging and Debugging

### Console Output
```
SOTATEK Quiz Extension: Starting automated quiz submission system...
SOTATEK Quiz Extension: Quiz data extracted: {quizHistoryId: 18428, answers: [...]}
SOTATEK Quiz Extension: Attempt 1/10
SOTATEK Quiz Extension: Submission result: {score: 25}
SOTATEK Quiz Extension: Found 5 incorrect answers, updating...
SOTATEK Quiz Extension: Perfect score achieved! 🎉
```

### Debug Information
- Quiz data extraction details
- API request/response logging
- Answer update strategies used
- Timing and retry information

## Security Considerations

### Authentication
- Tokens extracted securely from browser storage
- No token storage in extension
- Fallback token for development/testing only

### API Usage
- Respects rate limiting
- Uses proper CORS headers
- Maintains session cookies

### Data Privacy
- No quiz data stored permanently
- Logging only to browser console
- No external data transmission (except OpenAI API)

## Troubleshooting

### Common Issues

#### "Could not extract quiz data"
- Ensure you're on a valid quiz page
- Check that questions are loaded
- Refresh page and try again

#### "Could not extract authentication token"
- Log in to SotaTek LMS first
- Check browser storage for tokens
- System will use fallback token if available

#### "Failed to achieve perfect score after X attempts"
- Check OpenAI API key configuration
- Verify quiz questions are answerable
- Review console logs for specific errors

### Debug Steps
1. Open browser developer tools (F12)
2. Check console for detailed logs
3. Verify network requests in Network tab
4. Ensure proper authentication

## Future Enhancements

### Planned Features
- **Custom Question Mapping**: Manual question ID configuration
- **Response Format Detection**: Automatic API response format detection
- **Performance Metrics**: Detailed timing and success rate tracking
- **Batch Processing**: Multiple quiz handling

### Optimization Opportunities
- **Caching**: Store successful answer patterns
- **Learning**: Improve AI prompts based on success rates
- **Parallel Processing**: Multiple answer strategies simultaneously

## Support

For issues or questions:
1. Check console logs for error details
2. Verify all prerequisites are met
3. Test with a fresh browser session
4. Review this documentation for troubleshooting steps

The system is designed to be robust and self-recovering, handling most edge cases automatically while providing detailed logging for any issues that arise.
