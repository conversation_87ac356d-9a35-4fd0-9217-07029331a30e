# Debug Guide - API Response Analysis

## Overview

The automated quiz submission system now includes comprehensive debugging to identify and fix the API response parsing issues. This guide explains how to use the debugging features to understand what the SotaTek LMS API is actually returning.

## 🔍 Debug Features Added

### 1. Complete API Response Logging
The system now logs the entire API response structure:
- Response type and keys
- Full JSON structure
- Score-related fields
- Question-level data detection

### 2. Response Analysis Methods
The system tries multiple parsing methods:
- **Detailed Results**: Per-question feedback arrays
- **Incorrect List**: Direct list of wrong question IDs  
- **Questions Array**: Question-level data with correctness
- **Score-Based**: Fallback using total score only

### 3. Smart Retry Strategy
When specific incorrect questions can't be identified:
- **Subset Updates**: Updates small groups of questions per attempt
- **Estimated Batches**: Uses score to estimate how many to update
- **Progressive Strategy**: Different patterns for each attempt

## 🚀 How to Debug

### Step 1: Enable Debug Mode
1. Open the SotaTek LMS quiz page
2. Open browser Developer Tools (F12)
3. Go to Console tab
4. Click the extension and start "Submit Quiz with Retry"

### Step 2: Monitor API Response
Look for these debug messages in the console:

```
🔍 DEBUG: Complete API Response Structure:
- Type: object
- Keys: ["score", "totalQuestions", "results"]
- Full Response: {
  "score": 25,
  "totalQuestions": 30,
  "results": [...]
}
```

### Step 3: Check Background Script
1. Go to `chrome://extensions/`
2. Find your extension
3. Click "Inspect views: service worker"
4. Look for SotaTek API response analysis:

```
🔍 DEBUG: SotaTek API Response Analysis
🔍 DEBUG: Response status: 200
🔍 DEBUG: Response keys: ["data", "success", "message"]
🔍 DEBUG: Score-related fields found: {"score": 25, "totalCorrect": 25}
```

## 📊 Common API Response Formats

### Format 1: Simple Score Response
```json
{
  "score": 25,
  "totalQuestions": 30,
  "success": true
}
```
**Debug Output**: Uses score-based analysis
**Strategy**: Smart retry with estimated incorrect count

### Format 2: Detailed Results Array
```json
{
  "results": [
    {"questionId": 849, "correct": true, "userAnswer": 2},
    {"questionId": 861, "correct": false, "userAnswer": 0},
    ...
  ],
  "totalScore": 25
}
```
**Debug Output**: Uses detailed results method
**Strategy**: Updates only specifically incorrect questions

### Format 3: Nested Data Structure
```json
{
  "data": {
    "quiz": {
      "score": 25,
      "questions": [
        {"id": 849, "isCorrect": true},
        {"id": 861, "isCorrect": false},
        ...
      ]
    }
  },
  "success": true
}
```
**Debug Output**: Detects nested data, analyzes questions array
**Strategy**: Question-level updates

## 🔧 Debugging Specific Issues

### Issue: "30 incorrect questions on every attempt"

**Symptoms**:
```
🔍 DEBUG: Identified 30 incorrect questions
🔍 DEBUG: Analysis method used: score_based_all_wrong
```

**Diagnosis**: The API response doesn't contain recognizable score fields

**Solution**: Check the debug logs for actual field names:
```
🔍 DEBUG: Response keys: ["data", "result", "status"]
🔍 DEBUG: Score-related fields found: {}
```

**Fix**: Update the score field detection in `analyzeIncorrectAnswers()`

### Issue: "No incorrect questions identified"

**Symptoms**:
```
🔍 DEBUG: Identified 0 incorrect questions
🔍 DEBUG: No incorrect questions identified, but score is not perfect
```

**Diagnosis**: The parsing logic doesn't match the API response format

**Solution**: Check which analysis method was used:
```
🔍 DEBUG: Analysis method used: detailed_results
🔍 DEBUG: Results array length: 30
```

**Fix**: Verify the correctness detection logic for that method

### Issue: Perfect score detection failing

**Symptoms**:
```
🔍 DEBUG: Perfect score check - perfectScore: false, actualScore: 0
🔍 DEBUG: Possible score values: [undefined, undefined, ...]
```

**Diagnosis**: Score field names don't match expectations

**Solution**: Check the potential score fields:
```
🔍 DEBUG: Potential score fields: ["totalScore", "correctCount"]
```

**Fix**: Add the actual field names to the perfect score detection logic

## 🛠️ Manual API Testing

### Test API Response Format
Run this in the browser console on the quiz page:

```javascript
// Extract current quiz data
const quizData = extractQuizData();
const authToken = extractAuthToken();

// Make test API call
fetch("https://api.lms.sotatek.com/quiz", {
  method: "POST",
  headers: {
    "authorization": `Bearer ${authToken}`,
    "content-type": "application/json"
  },
  body: JSON.stringify({
    quizHistoryId: quizData.quizHistoryId,
    answers: quizData.answers.slice(0, 5) // Test with first 5 questions
  })
})
.then(r => r.json())
.then(result => {
  console.log("🔍 MANUAL TEST: API Response:", result);
  console.log("🔍 MANUAL TEST: Response keys:", Object.keys(result));
  console.log("🔍 MANUAL TEST: Full structure:", JSON.stringify(result, null, 2));
})
.catch(console.error);
```

### Test Response Analysis
```javascript
// Test the analysis function with a mock response
const mockResponse = {
  // Put the actual API response here
  score: 25,
  totalQuestions: 30
};

const mockAnswers = extractQuizData().answers;
const result = analyzeIncorrectAnswers(mockResponse, mockAnswers);
console.log("🔍 ANALYSIS TEST:", result);
```

## 📈 Performance Monitoring

### Monitor Retry Efficiency
Look for these patterns in the logs:

**Efficient (Good)**:
```
🔍 DEBUG: Identified 5 incorrect questions
🔍 DEBUG: Analysis method used: detailed_results
SOTATEK Quiz Extension: Found 5 incorrect answers, updating...
```

**Inefficient (Needs Fix)**:
```
🔍 DEBUG: Identified 30 incorrect questions  
🔍 DEBUG: Analysis method used: score_based_all_wrong
SOTATEK Quiz Extension: Found 30 incorrect answers, updating...
```

### Smart Retry Strategy Monitoring
```
🔍 DEBUG: Creating smart retry strategy for attempt 2
🔍 DEBUG: Estimated incorrect count: 5
🔍 DEBUG: Smart retry strategy created 15 questions to update
🔍 DEBUG: Strategy details: [{"index": 1, "strategy": "every_second"}, ...]
```

## 🎯 Optimization Tips

### 1. Identify the Correct API Format
- Run the manual API test
- Check background script logs
- Look for consistent field names across responses

### 2. Update Parsing Logic
Based on the actual API format, update these functions:
- `analyzeIncorrectAnswers()` - Add new response format detection
- Perfect score detection - Add actual score field names
- Question ID mapping - Ensure correct question identification

### 3. Test Incrementally
- Start with manual API calls
- Test with small question subsets
- Verify parsing logic before full automation

### 4. Monitor Success Rates
- Track how many attempts are needed
- Check if specific questions consistently fail
- Optimize retry strategies based on patterns

## 🚨 Common Fixes

### Fix 1: Add New Score Field
If the API uses a different score field:

```javascript
// In analyzeIncorrectAnswers(), add to score detection:
else if (apiResponse.yourActualScoreField !== undefined) {
  correctCount = apiResponse.yourActualScoreField;
  console.log("🔍 DEBUG: Using 'yourActualScoreField':", correctCount);
}
```

### Fix 2: Add New Response Format
If the API has a different structure:

```javascript
// Add new method in analyzeIncorrectAnswers():
else if (apiResponse.yourResponseFormat && Array.isArray(apiResponse.yourResponseFormat)) {
  analysisMethod = "your_format";
  // Add parsing logic here
}
```

### Fix 3: Update Perfect Score Detection
```javascript
// In perfect score check, add:
if (result.yourScoreField === 30 || result.yourPercentageField === 100) {
  perfectScore = true;
  actualScore = result.yourScoreField || 30;
}
```

The comprehensive debugging system will help you identify exactly what the SotaTek LMS API is returning and fix the parsing logic accordingly.
