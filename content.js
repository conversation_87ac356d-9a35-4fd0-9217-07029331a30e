// Add console log to verify content script is loaded
console.log("SOTATEK Quiz Extension: Content script loaded");

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  console.log("SOTATEK Quiz Extension: Message received", request);

  if (request.action === "answerQuiz") {
    answerQuiz()
      .then((result) => {
        console.log("SOTATEK Quiz Extension: Quiz completed", result);
        sendResponse(result);
      })
      .catch((error) => {
        console.error("SOTATEK Quiz Extension: Error", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }
});

async function answerQuiz() {
  try {
    // Get API key from storage
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      throw new Error("Please set your OpenAI API key first");
    }

    // Find all questions on the page
    const questions = extractQuestions();

    if (questions.length === 0) {
      throw new Error("No questions found on this page");
    }

    console.log(
      `SOTATEK Quiz Extension: Found ${questions.length} questions, processing in batch...`
    );

    // Process all questions in a single batch API call
    const batchAnswers = await answerAllQuestionsBatch(
      questions,
      result.openaiApiKey
    );

    // Apply answers to each question
    applyBatchAnswers(questions, batchAnswers);

    return { success: true, answeredCount: questions.length };
  } catch (error) {
    console.error("Quiz answering failed:", error);
    return { success: false, error: error.message };
  }
}

function extractQuestions() {
  const questions = [];

  // Look for question containers
  const questionElements = document.querySelectorAll("p");

  questionElements.forEach((questionEl) => {
    const questionText = questionEl.textContent.trim();

    // Check if this looks like a question
    if (
      questionText.includes("Question") ||
      questionText.includes("Câu hỏi") ||
      questionText.includes(":")
    ) {
      const questionContainer =
        questionEl.closest(".flex.flex-col.gap-4") || questionEl.parentElement;

      if (questionContainer) {
        const checkboxes = questionContainer.querySelectorAll(
          'button[role="checkbox"]'
        );
        const labels = questionContainer.querySelectorAll("label");

        if (checkboxes.length > 0 && labels.length > 0) {
          const options = [];

          labels.forEach((label, index) => {
            const labelText = label.textContent.trim();
            if (labelText && !labelText.includes("There must be at least")) {
              options.push({
                text: labelText,
                checkbox: checkboxes[index],
                label: label,
              });
            }
          });

          if (options.length > 0) {
            questions.push({
              questionText: questionText,
              options: options,
              container: questionContainer,
            });
          }
        }
      }
    }
  });

  return questions;
}

function extractCourseContent() {
  try {
    // Look for course content in various common selectors
    const contentSelectors = [
      ".course-content",
      ".lecture-content",
      ".lesson-content",
      ".content-body",
      ".main-content",
      "article",
      ".text-content",
      '[class*="content"]',
      "main",
    ];

    let courseContent = "";

    // Try to find course content
    for (const selector of contentSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim();
        if (
          text &&
          text.length > 100 &&
          !text.includes("Question") &&
          !text.includes("Câu hỏi")
        ) {
          courseContent += text + "\n\n";
          if (courseContent.length > 3000) break; // Limit content length
        }
      }
      if (courseContent.length > 500) break; // Found enough content
    }

    // If no specific content found, try to get general page text
    if (!courseContent) {
      const bodyText = document.body.textContent || "";
      const lines = bodyText.split("\n").filter((line) => {
        const trimmed = line.trim();
        return (
          trimmed.length > 50 &&
          !trimmed.includes("Question") &&
          !trimmed.includes("Câu hỏi") &&
          !trimmed.includes("button") &&
          !trimmed.includes("click")
        );
      });
      courseContent = lines.slice(0, 10).join("\n");
    }

    // Clean and limit the content
    return courseContent.substring(0, 1000).trim();
  } catch (error) {
    console.error("Error extracting course content:", error);
    return "";
  }
}

// SOTATEK ISMS & Software Development Security Reference
// Based on actual SOTATEK documents: "CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN" & "BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM"
const SOTATEK_SECURITY_REFERENCE = `
SOTATEK INFORMATION SECURITY MANAGEMENT SYSTEM (ISMS) REFERENCE

ĐỊNH NGHĨA AN TOÀN THÔNG TIN:
- Thông tin theo ISO/IEC 27001:2022: "Thông tin là một loại tài sản cũng như các tài sản khác đều có giá trị đối với một tổ chức và cần được bảo vệ thích hợp"
- Bảo vệ hệ thống thông tin chống lại: Truy cập trái phép, Sử dụng trái phép, Chỉnh sửa trái phép, Phá hủy, Làm lộ thông tin
- Ba tính chất: Tính bí mật (Confidentiality), Tính toàn vẹn (Integrity), Tính sẵn sàng (Availability)

CIA TRIAD - BA TÍNH CHẤT CƠ BẢN (CHI TIẾT):
- Tính bí mật (Confidentiality): Thông tin chỉ được truy cập bởi những người được phép, ngăn chặn tiết lộ trái phép
- Tính toàn vẹn (Integrity): Thông tin được duy trì chính xác và đầy đủ, KHÔNG BỊ THAY ĐỔI hoặc HƯ HỎNG
- Tính sẵn sàng (Availability): Thông tin có sẵn khi cần thiết, hệ thống hoạt động bình thường

VI PHẠM CIA TRIAD - CÁC TRƯỜNG HỢP CỤ THỂ:
- Vi phạm Confidentiality: Rò rỉ thông tin, truy cập trái phép, chia sẻ với người không được phép
- Vi phạm Integrity: File dữ liệu bị hỏng/corrupt, virus làm hư hại dữ liệu, thay đổi trái phép nội dung
- Vi phạm Availability: Hệ thống không truy cập được, server down, mạng bị gián đoạn

VIRUS VÀ MALWARE - TÁC ĐỘNG LÊN CIA:
- USB có virus làm hỏng file dữ liệu = VI PHẠM INTEGRITY (dữ liệu bị corrupt/hư hỏng)
- Ransomware mã hóa file = VI PHẠM AVAILABILITY (không truy cập được dữ liệu)
- Spyware đánh cắp thông tin = VI PHẠM CONFIDENTIALITY (rò rỉ thông tin)

PHÂN LOẠI TÀI SẢN VÀ THÔNG TIN:
- Tài sản SOTATEK: Tài sản con người, Tài sản thông tin, Tài sản phần mềm, Tài sản dịch vụ, Tài sản vật lý
- Phân loại bảo mật: Công khai (mọi người), Nội bộ (nhân viên SOTATEK), Mật (cần bảo mật), Tuyệt mật (ký cam kết)
- Quy định: Không chia sẻ thông tin dự án cho người không liên quan, Cấm sử dụng thiết bị ngoài mục đích dự án

THÔNG TIN NỘI BỘ VÀ BẢO MẬT:
- Thông tin lương thưởng: THUỘC LOẠI MẬT - chỉ dành cho nhân viên có thẩm quyền, TUYỆT ĐỐI KHÔNG được chia sẻ với khách hàng
- Thông tin tài chính nội bộ: THUỘC LOẠI MẬT - bao gồm lương, thưởng, chi phí, ngân sách công ty
- Thông tin nhân sự: THUỘC LOẠI MẬT - thông tin cá nhân nhân viên, đánh giá hiệu suất, quyết định nhân sự
- Chia sẻ thông tin MẬT với bên ngoài (khách hàng, đối tác): VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN

BẢO MẬT PHÁT TRIỂN PHẦN MỀM:
- Mục đích: Đảm bảo an toàn an ninh thông tin trong vòng đời phát triển phần mềm
- Phạm vi: Tất cả sản phẩm phần mềm của khối sản xuất SOTATEK, hệ thống thông tin, mã nguồn mở
- Phân loại lỗ hổng: Nghiêm trọng (xử lý ngay), Trung bình (lên kế hoạch xử lý), Thấp (có thể chấp nhận)
- Vá lỗi: Cài đặt bản vá lỗi bảo mật, Cập nhật tự động cho tất cả hệ thống
- Secure SDLC: Review/rà soát source code, Tool quản lý phải ghi chép trong văn bản quản lý cấu hình

QUY ĐỊNH MÃ NGUỒN MỞ VÀ THƯ VIỆN BÊN THỨ 3:
- Nếu khách hàng cho phép: Dự án phải tuân theo thỏa thuận cấp phép của mã/thư viện đó và phải lấy mã cấp phép nếu cần
- Nếu khách hàng không chỉ định: Quản lý dự án có thể tạo báo cáo đánh giá DAR (decision analysis resolution) để lựa chọn mã/thư viện phù hợp
- Nếu khách hàng không cho phép: Dự án tuyệt đối không được sử dụng bất kỳ mã nguồn/thư viện bên ngoài nào

QUY ĐỊNH SOURCE CODE VÀ GITHUB:
- Source code: Chỉ sử dụng Github của SOTATEK để lưu trữ, commit SC trước khi đẩy cho khách hàng
- Tạo repositories mới: PM phải yêu cầu và xin phê duyệt người có thẩm quyền (CTO, DD, Vice DD)
- Github khách hàng: Phải tuân thủ quy định tool của khách, dùng account tổ chức theo cấu trúc name/sotatek-dev
- Phần mềm: Chỉ cài phần mềm trong danh mục được phép, cần email xác nhận Trưởng/phó bộ phận + cc IT và ISMS

TRÁCH NHIỆM QUẢN LÝ SOURCE CODE REPOSITORY:
- TRÁCH NHIỆM CHÍNH: Project Manager (PM) chịu trách nhiệm chính trong việc quản lý source code repository
- PM có quyền: Tạo repo mới, phân quyền truy cập, quản lý branch, merge policy, backup strategy
- Developer Lead: Hỗ trợ PM trong technical review, code quality, nhưng KHÔNG phải người chịu trách nhiệm chính
- Developer: Thực hiện commit, push code theo quy định của PM
- Tester: Truy cập repo để test, không có quyền quản lý repo
- Quy định rõ: PM là người quản lý repository, Developer Lead chỉ là technical support

QUY ĐỊNH MÃ HÓA VÀ BẢO MẬT DỮ LIỆU:
- Mail đính kèm: Khi đính kèm dữ liệu cần mã hóa thông qua phương tiện khác gửi người nhận
- Quản lý tài sản: Trước khi cho mượn, xác nhận tài sản không chứa thông tin cá nhân/dữ liệu raw
- Trích xuất dữ liệu: Khi trích xuất khỏi môi trường phát triển phải mã hóa với mức độ bảo mật cao

BẢO VỆ DỮ LIỆU:
- Lưu trữ: Thông tin trước bàn giao phải lưu trữ an toàn, chỉ phân quyền nhân viên dự án
- Bàn giao: Đảm bảo bàn giao đúng thông tin theo mốc thống nhất với khách hàng

QUY ĐỊNH MÁY TÍNH:
- Phạm vi: Máy tính để bàn, laptop tại SOTATEK, máy tính cá nhân
- Yêu cầu: Screensaver tối đa 5 phút, Password an toàn, Phần mềm có bản quyền, Antivirus tự động
- Bảo mật: Khóa quyền quản trị (IT quản lý), Phần mềm chống mã độc tập trung
- Cấm: Can thiệp thay đổi quyền quản trị không qua CNTT = xâm phạm an toàn thông tin

QUY ĐỊNH PASSWORD (CHI TIẾT):
- Phạm vi: Tất cả mật khẩu hệ thống ISMS SOTATEK
- Quy định chung: Account duy nhất cho mỗi user, Không dùng chung account, Báo sự cố mật khẩu cho IT/manager ngay
- Yêu cầu mật khẩu: Tối thiểu 8 ký tự, Thay đổi <90 ngày, Độ phức tạp 3-4 yêu cầu (ký tự in hoa + ký tự in thường + ký tự số + ký tự đặc biệt)
- Trường hợp ngoại lệ: Mật khẩu hệ thống quản trị mạng 4 ký tự bằng số, Thiết bị mạng (load balance, router, Switch) thay đổi 6 tháng/lần

QUY ĐỊNH EMAIL & MẠNG (CHI TIẾT):
- Email: Không cho mượn tài khoản, Không dùng tài khoản người khác, Không gửi spam email, Không mở link lạ/email phishing
- Không gửi/nhận thông tin vi phạm thuần phong mỹ tục/an ninh quốc phòng, Xin phê duyệt trước hoặc CC để có bản ghi
- Mạng nội bộ: Chỉ truy cập dữ liệu được phân quyền, Không website vi phạm văn hóa/quy định quốc gia
- Internet: Sử dụng Proxy công ty, Nghiêm cấm tools vượt tường lửa/proxy (FreeProxy, FreeGate, UltraSurf...)

QUY ĐỊNH LÀM VIỆC BÊN NGOÀI (CHI TIẾT):
- Phạm vi: Laptop SOTATEK, máy tính cá nhân sử dụng cho công việc
- Yêu cầu chung: Phải đảm bảo an toàn thông tin và dữ liệu công ty, Chịu trách nhiệm với dữ liệu công ty trong laptop
- Bảo mật laptop: Cập nhật bản vá lỗi hàng ngày, Mã hóa dữ liệu ổ cứng, Đặt mật khẩu ổ đĩa
- Làm việc với khách hàng: Cần sự đồng thuận từ khách hàng, Dữ liệu mang đi phải ở trạng thái đã mã hóa

QUY ĐỊNH BÀN LÀM VIỆC:
- Khi rời bàn: Cất tài sản quan trọng vào nơi an toàn, Khóa màn hình máy tính
- Tài liệu: Không để quên tài liệu ngoài bàn làm việc
- Khách tham quan: Thu xếp tài liệu, thông tin ngoài tầm mắt khách
- Công văn: Lưu tủ có khóa, Lấy ngay fax ra khỏi máy, Không để thông tin dự án cho người ngoài xem

AN TOÀN VẬT LÝ:
- Khu vực xanh (chung): Mở, không hạn chế, tất cả nhân viên và khách
- Khu vực vàng (hạn chế): Làm việc riêng, chỉ nhân viên được phân công
- Khu vực đỏ (bảo mật): Mức độ an ninh cao, chỉ nhân viên có thẩm quyền, ghi log ra vào
- Quy định: Không đưa bạn bè/người thân đến, Đăng ký OT/làm đêm với Admin, Không chụp ảnh/quay phim

XỬ LÝ SỰ CỐ AN TOÀN THÔNG TIN:
- Quy trình: Phát hiện → Báo cáo ngay → Không tự ý giải quyết → Hợp tác với IT/ISMS → Rút kinh nghiệm
- Căn cứ xử phạt: Mức độ rủi ro (rò rỉ/mất mát thông tin), Mức độ thiệt hại, Tính chất lỗi (cố ý/vô ý)
- Hình thức xử phạt: Đánh giá điểm PA hàng tháng, Phạt tiền 100.000đ-1.000.000đ, Bồi thường thiệt hại, Kỷ luật lao động
- Mức nặng nhất: Buộc thôi việc, đưa ra tòa án, xác định bồi thường

NGUYÊN TẮC ĐÁNH GIÁ VI PHẠM BẢO MẬT:
- VI PHẠM NGHIÊM TRỌNG: Chia sẻ thông tin MẬT/TUYỆT MẬT với bên ngoài (bất kể có ý đồ hay vô tình)
- Thông tin lương thưởng với khách hàng = VI PHẠM NGHIÊM TRỌNG (không phụ thuộc vào ý đồ)
- Thông tin tài chính nội bộ với bên ngoài = VI PHẠM NGHIÊM TRỌNG (không phụ thuộc vào ý đồ)
- Nguyên tắc: MỨC ĐỘ THIỆT HẠI và PHÂN LOẠI THÔNG TIN quan trọng hơn Ý ĐỊNH của nhân viên
- Vô tình chia sẻ thông tin MẬT = vẫn là VI PHẠM NGHIÊM TRỌNG vì gây rủi ro cao cho công ty

SỰ CỐ CỤ THỂ VÀ XỬ LÝ:
- Virus/Malware: Thay đổi password, báo manager và IT, IT rà soát 100% máy tính cài antivirus bản quyền
- Phishing: Không click link khả nghi, họp nhắc nhở cá nhân, phổ biến quy định BMTT toàn công ty
- Rò rỉ thông tin khách hàng: Ảnh hưởng sale phải giải thích, khách hàng mất niềm tin, dự án có thể trì hoãn
- Trao đổi với khách hàng: Phải qua Sale/PM hoặc CC công khai, không tự ý trao đổi ngoài phạm vi dự án

VÍ DỤ VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN:
- Nhân viên vô tình chia sẻ thông tin lương thưởng với khách hàng = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Nhân viên vô tình tiết lộ chi phí dự án với đối tác = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Nhân viên vô tình chia sẻ thông tin nhân sự với bên ngoài = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Lý do: Thông tin này thuộc loại MẬT, việc chia sẻ với bên ngoài luôn là VI PHẠM NGHIÊM TRỌNG bất kể ý định

VÍ DỤ CỤ THỂ VỀ CIA TRIAD VÀ QUẢN LÝ REPOSITORY:
- Nhân viên A cắm USB có virus vào máy công ty, file dữ liệu bị hỏng = VI PHẠM INTEGRITY (dữ liệu corrupt)
- Trách nhiệm quản lý source code repository thuộc về = PROJECT MANAGER (không phải Developer Lead)
- Server down không truy cập được hệ thống = VI PHẠM AVAILABILITY
- Hacker đánh cắp database khách hàng = VI PHẠM CONFIDENTIALITY
- PM quyết định tạo repo mới, phân quyền, merge policy = TRÁCH NHIỆM CHÍNH của Project Manager
`;

async function answerAllQuestionsBatch(questions, apiKey, retryCount = 0) {
  try {
    // Extract course content from the page for context
    const courseContent = extractCourseContent();

    // Build comprehensive prompt with all questions
    const batchPrompt = buildBatchPrompt(questions, courseContent);

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content:
              "You are an expert in SOTATEK's Information Security Management System (ISMS) and Software Development Security policies based on the actual Vietnamese organizational documents 'CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN' and 'BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM'. Use the comprehensive SOTATEK reference material extracted from these actual documents to answer quiz questions accurately. The reference includes SOTATEK's specific Vietnamese policies for information security definitions, asset classification, software development security, computer usage, password requirements, email/network usage, remote work, desk security, physical security zones, and incident handling procedures. IMPORTANT: Only provide multiple letters if the question text explicitly contains '(chọn nhiều đáp án)' - otherwise always respond with just ONE letter (A, B, C, or D) even if multiple answers seem correct. Always ensure answers precisely match SOTATEK's documented organizational policies and procedures. For batch responses, format as 'Question 1: A, Question 2: B C, Question 3: D' where multiple letters indicate multiple choice questions.",
          },
          {
            role: "user",
            content: batchPrompt,
          },
        ],
        temperature: 0,
        max_tokens: 500,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle rate limiting (429) with exponential backoff
      if (response.status === 429 && retryCount < 3) {
        const waitTime = Math.pow(2, retryCount) * 3000; // 3s, 6s, 12s
        console.log(
          `Rate limited. Waiting ${waitTime}ms before retry ${
            retryCount + 1
          }/3...`
        );
        await new Promise((resolve) => setTimeout(resolve, waitTime));
        return answerAllQuestionsBatch(questions, apiKey, retryCount + 1);
      }

      throw new Error(
        `OpenAI API error: ${response.status} - ${
          errorData.error?.message || "Unknown error"
        }`
      );
    }

    const data = await response.json();
    const batchAnswer = data.choices[0].message.content.trim();

    console.log(
      "SOTATEK Quiz Extension: Batch response received:",
      batchAnswer
    );

    // Parse the batch response into individual answers
    return parseBatchResponse(batchAnswer, questions.length);
  } catch (error) {
    console.error("Error in batch API call:", error);
    throw error;
  }
}

function buildBatchPrompt(questions, courseContent) {
  const prompt = `You are answering a quiz about SOTATEK's Information Security Management System (ISMS) and Software Development Security policies based on the actual organizational documents "CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN" and "BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM". Use the comprehensive SOTATEK reference material to provide the most accurate answers based on the organization's specific Vietnamese policies and procedures.

SOTATEK ISMS & SOFTWARE DEVELOPMENT SECURITY REFERENCE (FROM ACTUAL DOCUMENTS):
${SOTATEK_SECURITY_REFERENCE}

${courseContent ? `COURSE CONTENT REFERENCE:\n${courseContent}\n\n` : ""}

QUIZ QUESTIONS:
${questions
  .map((question, index) => {
    const questionNumber = index + 1;
    const isMultipleChoice = question.questionText.includes(
      "(chọn nhiều đáp án)"
    );
    const questionType = isMultipleChoice
      ? " [MULTIPLE CHOICE]"
      : " [SINGLE CHOICE]";

    return `Question ${questionNumber}${questionType}: ${question.questionText}

OPTIONS:
${question.options
  .map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt.text}`)
  .join("\n")}`;
  })
  .join("\n\n")}

INSTRUCTIONS:
- For SINGLE CHOICE questions: Respond with format "Question X: A" (only one letter)
- For MULTIPLE CHOICE questions (marked with "(chọn nhiều đáp án)"): Respond with format "Question X: A B C" (multiple letters separated by spaces)
- Base all answers on SOTATEK's specific Vietnamese ISMS policies and procedures documented above
- Ensure answers precisely align with SOTATEK's organizational policies

Please provide answers for all questions in the format: "Question 1: A, Question 2: B C, Question 3: D"`;

  return prompt;
}

function parseBatchResponse(batchResponse, expectedQuestionCount) {
  const answers = {};

  // Try to extract answers in format "Question X: LETTERS"
  const questionPattern = /Question\s+(\d+):\s*([A-Z\s]+)/gi;
  let match;

  while ((match = questionPattern.exec(batchResponse)) !== null) {
    const questionNumber = parseInt(match[1]);
    const answerLetters = match[2].trim().split(/\s+/); // Split by whitespace
    answers[questionNumber] = answerLetters;
  }

  // Fallback: try to extract just letters in order
  if (Object.keys(answers).length === 0) {
    const letterMatches = batchResponse.match(/[A-Z]/g);
    if (letterMatches) {
      for (
        let i = 0;
        i < Math.min(letterMatches.length, expectedQuestionCount);
        i++
      ) {
        answers[i + 1] = [letterMatches[i]];
      }
    }
  }

  console.log("SOTATEK Quiz Extension: Parsed answers:", answers);
  return answers;
}

function applyBatchAnswers(questions, batchAnswers) {
  questions.forEach((question, index) => {
    const questionNumber = index + 1;
    const answers = batchAnswers[questionNumber];

    if (answers && answers.length > 0) {
      // Check if this is explicitly a multiple choice question
      const isMultipleChoice = question.questionText.includes(
        "(chọn nhiều đáp án)"
      );

      if (isMultipleChoice) {
        // Multiple choice - select all provided answers
        console.log(
          `Question ${questionNumber}: Multiple choice - selecting ${answers.join(
            ", "
          )}`
        );
        selectMultipleAnswers(question, answers);
      } else {
        // Single choice - select only the first answer
        console.log(
          `Question ${questionNumber}: Single choice - selecting ${answers[0]}`
        );
        selectSingleAnswer(question, answers[0]);
      }
    } else {
      console.warn(`No answer found for question ${questionNumber}`);
    }
  });
}

function selectSingleAnswer(question, answerLetter) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Select the specified answer
  const index = answerLetter.charCodeAt(0) - 65; // A=0, B=1, etc.
  if (index >= 0 && index < question.options.length) {
    question.options[index].checkbox.click();
  }
}

function selectMultipleAnswers(question, answerLetters) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Select all specified answers
  answerLetters.forEach((letter) => {
    const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
    if (index >= 0 && index < question.options.length) {
      question.options[index].checkbox.click();
    }
  });
}

// Legacy single question function - kept for fallback if needed
// Now using batch processing in answerAllQuestionsBatch() instead

// Legacy selectAnswers function - replaced by selectSingleAnswer and selectMultipleAnswers for batch processing
