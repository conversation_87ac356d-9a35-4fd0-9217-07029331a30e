// Add console log to verify content script is loaded
console.log("SOTATEK Quiz Extension: Content script loaded");

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  console.log("SOTATEK Quiz Extension: Message received", request);

  if (request.action === "answerQuiz") {
    answerQuiz()
      .then((result) => {
        console.log("SOTATEK Quiz Extension: Quiz completed", result);
        sendResponse(result);
      })
      .catch((error) => {
        console.error("SOTATEK Quiz Extension: Error", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }

  if (request.action === "submitQuizWithRetry") {
    submitQuizWithRetry()
      .then((result) => {
        console.log(
          "SOTATEK Quiz Extension: Quiz submission completed",
          result
        );
        sendResponse(result);
      })
      .catch((error) => {
        console.error("SOTATEK Quiz Extension: Submission error", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }

  if (request.action === "completeQuizAutomatically") {
    completeQuizAutomatically()
      .then((result) => {
        console.log(
          "SOTATEK Quiz Extension: Complete quiz automation finished",
          result
        );
        sendResponse(result);
      })
      .catch((error) => {
        console.error(
          "SOTATEK Quiz Extension: Complete automation error",
          error
        );
        sendResponse({ success: false, error: error.message });
      });
    return true; // Keep message channel open for async response
  }
});

async function answerQuiz() {
  try {
    // Get API key from storage
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      throw new Error("Please set your OpenAI API key first");
    }

    // Find all questions on the page
    const questions = extractQuestions();

    if (questions.length === 0) {
      throw new Error("No questions found on this page");
    }

    console.log(
      `SOTATEK Quiz Extension: Found ${questions.length} questions, processing in batch...`
    );

    // Process all questions in a single batch API call
    const batchAnswers = await answerAllQuestionsBatch(
      questions,
      result.openaiApiKey
    );

    // Apply answers to each question
    applyBatchAnswers(questions, batchAnswers);

    return { success: true, answeredCount: questions.length };
  } catch (error) {
    console.error("Quiz answering failed:", error);
    return { success: false, error: error.message };
  }
}

function extractQuestions() {
  const questions = [];

  // Look for question containers
  const questionElements = document.querySelectorAll("p");

  questionElements.forEach((questionEl) => {
    const questionText = questionEl.textContent.trim();

    // Check if this looks like a question
    if (
      questionText.includes("Question") ||
      questionText.includes("Câu hỏi") ||
      questionText.includes(":")
    ) {
      const questionContainer =
        questionEl.closest(".flex.flex-col.gap-4") || questionEl.parentElement;

      if (questionContainer) {
        const checkboxes = questionContainer.querySelectorAll(
          'button[role="checkbox"]'
        );
        const labels = questionContainer.querySelectorAll("label");

        if (checkboxes.length > 0 && labels.length > 0) {
          const options = [];

          labels.forEach((label, index) => {
            const labelText = label.textContent.trim();
            if (labelText && !labelText.includes("There must be at least")) {
              options.push({
                text: labelText,
                checkbox: checkboxes[index],
                label: label,
              });
            }
          });

          if (options.length > 0) {
            questions.push({
              questionText: questionText,
              options: options,
              container: questionContainer,
            });
          }
        }
      }
    }
  });

  return questions;
}

function extractCourseContent() {
  try {
    // Look for course content in various common selectors
    const contentSelectors = [
      ".course-content",
      ".lecture-content",
      ".lesson-content",
      ".content-body",
      ".main-content",
      "article",
      ".text-content",
      '[class*="content"]',
      "main",
    ];

    let courseContent = "";

    // Try to find course content
    for (const selector of contentSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.textContent?.trim();
        if (
          text &&
          text.length > 100 &&
          !text.includes("Question") &&
          !text.includes("Câu hỏi")
        ) {
          courseContent += text + "\n\n";
          if (courseContent.length > 3000) break; // Limit content length
        }
      }
      if (courseContent.length > 500) break; // Found enough content
    }

    // If no specific content found, try to get general page text
    if (!courseContent) {
      const bodyText = document.body.textContent || "";
      const lines = bodyText.split("\n").filter((line) => {
        const trimmed = line.trim();
        return (
          trimmed.length > 50 &&
          !trimmed.includes("Question") &&
          !trimmed.includes("Câu hỏi") &&
          !trimmed.includes("button") &&
          !trimmed.includes("click")
        );
      });
      courseContent = lines.slice(0, 10).join("\n");
    }

    // Clean and limit the content
    return courseContent.substring(0, 1000).trim();
  } catch (error) {
    console.error("Error extracting course content:", error);
    return "";
  }
}

// SOTATEK ISMS & Software Development Security Reference
// Based on actual SOTATEK documents: "CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN" & "BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM"
const SOTATEK_SECURITY_REFERENCE = `
SOTATEK INFORMATION SECURITY MANAGEMENT SYSTEM (ISMS) REFERENCE

ĐỊNH NGHĨA AN TOÀN THÔNG TIN:
- Thông tin theo ISO/IEC 27001:2022: "Thông tin là một loại tài sản cũng như các tài sản khác đều có giá trị đối với một tổ chức và cần được bảo vệ thích hợp"
- Bảo vệ hệ thống thông tin chống lại: Truy cập trái phép, Sử dụng trái phép, Chỉnh sửa trái phép, Phá hủy, Làm lộ thông tin
- Ba tính chất: Tính bí mật (Confidentiality), Tính toàn vẹn (Integrity), Tính sẵn sàng (Availability)

CIA TRIAD - BA TÍNH CHẤT CƠ BẢN (CHI TIẾT):
- Tính bí mật (Confidentiality): Thông tin chỉ được truy cập bởi những người được phép, ngăn chặn tiết lộ trái phép
- Tính toàn vẹn (Integrity): Thông tin được duy trì chính xác và đầy đủ, KHÔNG BỊ THAY ĐỔI hoặc HƯ HỎNG
- Tính sẵn sàng (Availability): Thông tin có sẵn khi cần thiết, hệ thống hoạt động bình thường

VI PHẠM CIA TRIAD - CÁC TRƯỜNG HỢP CỤ THỂ:
- Vi phạm Confidentiality: Rò rỉ thông tin, truy cập trái phép, chia sẻ với người không được phép
- Vi phạm Integrity: File dữ liệu bị hỏng/corrupt, virus làm hư hại dữ liệu, thay đổi trái phép nội dung
- Vi phạm Availability: Hệ thống không truy cập được, server down, mạng bị gián đoạn

VIRUS VÀ MALWARE - TÁC ĐỘNG LÊN CIA:
- USB có virus làm hỏng file dữ liệu = VI PHẠM INTEGRITY (dữ liệu bị corrupt/hư hỏng)
- Ransomware mã hóa file = VI PHẠM AVAILABILITY (không truy cập được dữ liệu)
- Spyware đánh cắp thông tin = VI PHẠM CONFIDENTIALITY (rò rỉ thông tin)

PHÂN LOẠI TÀI SẢN VÀ THÔNG TIN:
- Tài sản SOTATEK: Tài sản con người, Tài sản thông tin, Tài sản phần mềm, Tài sản dịch vụ, Tài sản vật lý
- Phân loại bảo mật: Công khai (mọi người), Nội bộ (nhân viên SOTATEK), Mật (cần bảo mật), Tuyệt mật (ký cam kết)
- Quy định: Không chia sẻ thông tin dự án cho người không liên quan, Cấm sử dụng thiết bị ngoài mục đích dự án

THÔNG TIN NỘI BỘ VÀ BẢO MẬT:
- Thông tin lương thưởng: THUỘC LOẠI MẬT - chỉ dành cho nhân viên có thẩm quyền, TUYỆT ĐỐI KHÔNG được chia sẻ với khách hàng
- Thông tin tài chính nội bộ: THUỘC LOẠI MẬT - bao gồm lương, thưởng, chi phí, ngân sách công ty
- Thông tin nhân sự: THUỘC LOẠI MẬT - thông tin cá nhân nhân viên, đánh giá hiệu suất, quyết định nhân sự
- Chia sẻ thông tin MẬT với bên ngoài (khách hàng, đối tác): VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN

BẢO MẬT PHÁT TRIỂN PHẦN MỀM:
- Mục đích: Đảm bảo an toàn an ninh thông tin trong vòng đời phát triển phần mềm
- Phạm vi: Tất cả sản phẩm phần mềm của khối sản xuất SOTATEK, hệ thống thông tin, mã nguồn mở
- Phân loại lỗ hổng: Nghiêm trọng (xử lý ngay), Trung bình (lên kế hoạch xử lý), Thấp (có thể chấp nhận)
- Vá lỗi: Cài đặt bản vá lỗi bảo mật, Cập nhật tự động cho tất cả hệ thống
- Secure SDLC: Review/rà soát source code, Tool quản lý phải ghi chép trong văn bản quản lý cấu hình

QUY ĐỊNH MÃ NGUỒN MỞ VÀ THƯ VIỆN BÊN THỨ 3:
- Nếu khách hàng cho phép: Dự án phải tuân theo thỏa thuận cấp phép của mã/thư viện đó và phải lấy mã cấp phép nếu cần
- Nếu khách hàng không chỉ định: Quản lý dự án có thể tạo báo cáo đánh giá DAR (decision analysis resolution) để lựa chọn mã/thư viện phù hợp
- Nếu khách hàng không cho phép: Dự án tuyệt đối không được sử dụng bất kỳ mã nguồn/thư viện bên ngoài nào

QUY ĐỊNH SOURCE CODE VÀ GITHUB:
- Source code: Chỉ sử dụng Github của SOTATEK để lưu trữ, commit SC trước khi đẩy cho khách hàng
- Tạo repositories mới: PM phải yêu cầu và xin phê duyệt người có thẩm quyền (CTO, DD, Vice DD)
- Github khách hàng: Phải tuân thủ quy định tool của khách, dùng account tổ chức theo cấu trúc name/sotatek-dev
- Phần mềm: Chỉ cài phần mềm trong danh mục được phép, cần email xác nhận Trưởng/phó bộ phận + cc IT và ISMS

TRÁCH NHIỆM QUẢN LÝ SOURCE CODE REPOSITORY:
- TRÁCH NHIỆM CHÍNH: Project Manager (PM) chịu trách nhiệm chính trong việc quản lý source code repository
- PM có quyền: Tạo repo mới, phân quyền truy cập, quản lý branch, merge policy, backup strategy
- Developer Lead: Hỗ trợ PM trong technical review, code quality, nhưng KHÔNG phải người chịu trách nhiệm chính
- Developer: Thực hiện commit, push code theo quy định của PM
- Tester: Truy cập repo để test, không có quyền quản lý repo
- Quy định rõ: PM là người quản lý repository, Developer Lead chỉ là technical support

QUY ĐỊNH MÃ HÓA VÀ BẢO MẬT DỮ LIỆU:
- Mail đính kèm: Khi đính kèm dữ liệu cần mã hóa thông qua phương tiện khác gửi người nhận
- Quản lý tài sản: Trước khi cho mượn, xác nhận tài sản không chứa thông tin cá nhân/dữ liệu raw
- Trích xuất dữ liệu: Khi trích xuất khỏi môi trường phát triển phải mã hóa với mức độ bảo mật cao

BẢO VỆ DỮ LIỆU:
- Lưu trữ: Thông tin trước bàn giao phải lưu trữ an toàn, chỉ phân quyền nhân viên dự án
- Bàn giao: Đảm bảo bàn giao đúng thông tin theo mốc thống nhất với khách hàng

QUY ĐỊNH MÁY TÍNH:
- Phạm vi: Máy tính để bàn, laptop tại SOTATEK, máy tính cá nhân
- Yêu cầu: Screensaver tối đa 5 phút, Password an toàn, Phần mềm có bản quyền, Antivirus tự động
- Bảo mật: Khóa quyền quản trị (IT quản lý), Phần mềm chống mã độc tập trung
- Cấm: Can thiệp thay đổi quyền quản trị không qua CNTT = xâm phạm an toàn thông tin

QUY ĐỊNH PASSWORD (CHI TIẾT):
- Phạm vi: Tất cả mật khẩu hệ thống ISMS SOTATEK
- Quy định chung: Account duy nhất cho mỗi user, Không dùng chung account, Báo sự cố mật khẩu cho IT/manager ngay
- Yêu cầu mật khẩu: Tối thiểu 8 ký tự, Thay đổi <90 ngày, Độ phức tạp 3-4 yêu cầu (ký tự in hoa + ký tự in thường + ký tự số + ký tự đặc biệt)
- Trường hợp ngoại lệ: Mật khẩu hệ thống quản trị mạng 4 ký tự bằng số, Thiết bị mạng (load balance, router, Switch) thay đổi 6 tháng/lần

QUY ĐỊNH EMAIL & MẠNG (CHI TIẾT):
- Email: Không cho mượn tài khoản, Không dùng tài khoản người khác, Không gửi spam email, Không mở link lạ/email phishing
- Không gửi/nhận thông tin vi phạm thuần phong mỹ tục/an ninh quốc phòng, Xin phê duyệt trước hoặc CC để có bản ghi
- Mạng nội bộ: Chỉ truy cập dữ liệu được phân quyền, Không website vi phạm văn hóa/quy định quốc gia
- Internet: Sử dụng Proxy công ty, Nghiêm cấm tools vượt tường lửa/proxy (FreeProxy, FreeGate, UltraSurf...)

QUY ĐỊNH LÀM VIỆC BÊN NGOÀI (CHI TIẾT):
- Phạm vi: Laptop SOTATEK, máy tính cá nhân sử dụng cho công việc
- Yêu cầu chung: Phải đảm bảo an toàn thông tin và dữ liệu công ty, Chịu trách nhiệm với dữ liệu công ty trong laptop
- Bảo mật laptop: Cập nhật bản vá lỗi hàng ngày, Mã hóa dữ liệu ổ cứng, Đặt mật khẩu ổ đĩa
- Làm việc với khách hàng: Cần sự đồng thuận từ khách hàng, Dữ liệu mang đi phải ở trạng thái đã mã hóa

QUY ĐỊNH BÀN LÀM VIỆC:
- Khi rời bàn: Cất tài sản quan trọng vào nơi an toàn, Khóa màn hình máy tính
- Tài liệu: Không để quên tài liệu ngoài bàn làm việc
- Khách tham quan: Thu xếp tài liệu, thông tin ngoài tầm mắt khách
- Công văn: Lưu tủ có khóa, Lấy ngay fax ra khỏi máy, Không để thông tin dự án cho người ngoài xem

AN TOÀN VẬT LÝ:
- Khu vực xanh (chung): Mở, không hạn chế, tất cả nhân viên và khách
- Khu vực vàng (hạn chế): Làm việc riêng, chỉ nhân viên được phân công
- Khu vực đỏ (bảo mật): Mức độ an ninh cao, chỉ nhân viên có thẩm quyền, ghi log ra vào
- Quy định: Không đưa bạn bè/người thân đến, Đăng ký OT/làm đêm với Admin, Không chụp ảnh/quay phim

XỬ LÝ SỰ CỐ AN TOÀN THÔNG TIN:
- Quy trình: Phát hiện → Báo cáo ngay → Không tự ý giải quyết → Hợp tác với IT/ISMS → Rút kinh nghiệm
- Căn cứ xử phạt: Mức độ rủi ro (rò rỉ/mất mát thông tin), Mức độ thiệt hại, Tính chất lỗi (cố ý/vô ý)
- Hình thức xử phạt: Đánh giá điểm PA hàng tháng, Phạt tiền 100.000đ-1.000.000đ, Bồi thường thiệt hại, Kỷ luật lao động
- Mức nặng nhất: Buộc thôi việc, đưa ra tòa án, xác định bồi thường

NGUYÊN TẮC ĐÁNH GIÁ VI PHẠM BẢO MẬT:
- VI PHẠM NGHIÊM TRỌNG: Chia sẻ thông tin MẬT/TUYỆT MẬT với bên ngoài (bất kể có ý đồ hay vô tình)
- Thông tin lương thưởng với khách hàng = VI PHẠM NGHIÊM TRỌNG (không phụ thuộc vào ý đồ)
- Thông tin tài chính nội bộ với bên ngoài = VI PHẠM NGHIÊM TRỌNG (không phụ thuộc vào ý đồ)
- Nguyên tắc: MỨC ĐỘ THIỆT HẠI và PHÂN LOẠI THÔNG TIN quan trọng hơn Ý ĐỊNH của nhân viên
- Vô tình chia sẻ thông tin MẬT = vẫn là VI PHẠM NGHIÊM TRỌNG vì gây rủi ro cao cho công ty

SỰ CỐ CỤ THỂ VÀ XỬ LÝ:
- Virus/Malware: Thay đổi password, báo manager và IT, IT rà soát 100% máy tính cài antivirus bản quyền
- Phishing: Không click link khả nghi, họp nhắc nhở cá nhân, phổ biến quy định BMTT toàn công ty
- Rò rỉ thông tin khách hàng: Ảnh hưởng sale phải giải thích, khách hàng mất niềm tin, dự án có thể trì hoãn
- Trao đổi với khách hàng: Phải qua Sale/PM hoặc CC công khai, không tự ý trao đổi ngoài phạm vi dự án

VÍ DỤ VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN:
- Nhân viên vô tình chia sẻ thông tin lương thưởng với khách hàng = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Nhân viên vô tình tiết lộ chi phí dự án với đối tác = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Nhân viên vô tình chia sẻ thông tin nhân sự với bên ngoài = VI PHẠM NGHIÊM TRỌNG BẢO MẬT THÔNG TIN
- Lý do: Thông tin này thuộc loại MẬT, việc chia sẻ với bên ngoài luôn là VI PHẠM NGHIÊM TRỌNG bất kể ý định

VÍ DỤ CỤ THỂ VỀ CIA TRIAD VÀ QUẢN LÝ REPOSITORY:
- Nhân viên A cắm USB có virus vào máy công ty, file dữ liệu bị hỏng = VI PHẠM INTEGRITY (dữ liệu corrupt)
- Trách nhiệm quản lý source code repository thuộc về = PROJECT MANAGER (không phải Developer Lead)
- Server down không truy cập được hệ thống = VI PHẠM AVAILABILITY
- Hacker đánh cắp database khách hàng = VI PHẠM CONFIDENTIALITY
- PM quyết định tạo repo mới, phân quyền, merge policy = TRÁCH NHIỆM CHÍNH của Project Manager
`;

async function answerAllQuestionsBatch(questions, apiKey, retryCount = 0) {
  try {
    // Extract course content from the page for context
    const courseContent = extractCourseContent();

    // Build comprehensive prompt with all questions
    const batchPrompt = buildBatchPrompt(questions, courseContent);

    // Use background script for OpenAI API call to avoid CORS issues
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(
        {
          action: "makeOpenAIRequest",
          data: {
            messages: [
              {
                role: "system",
                content:
                  "You are an expert in SOTATEK's Information Security Management System (ISMS) and Software Development Security policies based on the actual Vietnamese organizational documents 'CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN' and 'BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM'. Use the comprehensive SOTATEK reference material extracted from these actual documents to answer quiz questions accurately. The reference includes SOTATEK's specific Vietnamese policies for information security definitions, asset classification, software development security, computer usage, password requirements, email/network usage, remote work, desk security, physical security zones, and incident handling procedures. IMPORTANT: Only provide multiple letters if the question text explicitly contains '(chọn nhiều đáp án)' - otherwise always respond with just ONE letter (A, B, C, or D) even if multiple answers seem correct. Always ensure answers precisely match SOTATEK's documented organizational policies and procedures. For batch responses, format as 'Question 1: A, Question 2: B C, Question 3: D' where multiple letters indicate multiple choice questions.",
              },
              {
                role: "user",
                content: batchPrompt,
              },
            ],
            apiKey: apiKey,
            model: "gpt-4o-mini",
            temperature: 0,
            maxTokens: 500,
          },
        },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        }
      );
    });

    if (!response.success) {
      // Handle rate limiting (429) with exponential backoff
      if (response.error && response.error.includes("429") && retryCount < 3) {
        const waitTime = Math.pow(2, retryCount) * 3000; // 3s, 6s, 12s
        console.log(
          `Rate limited. Waiting ${waitTime}ms before retry ${
            retryCount + 1
          }/3...`
        );
        await new Promise((resolve) => setTimeout(resolve, waitTime));
        return answerAllQuestionsBatch(questions, apiKey, retryCount + 1);
      }

      throw new Error(response.error || "OpenAI API call failed");
    }

    const data = response.data;
    const batchAnswer = data.choices[0].message.content.trim();

    console.log(
      "SOTATEK Quiz Extension: Batch response received:",
      batchAnswer
    );

    // Parse the batch response into individual answers
    return parseBatchResponse(batchAnswer, questions.length);
  } catch (error) {
    console.error("Error in batch API call:", error);
    throw error;
  }
}

function buildBatchPrompt(questions, courseContent) {
  const prompt = `You are answering a quiz about SOTATEK's Information Security Management System (ISMS) and Software Development Security policies based on the actual organizational documents "CÁC QUY ĐỊNH VỀ AN TOÀN THÔNG TIN" and "BẢO MẬT TRONG PHÁT TRIỂN PHẦN MỀM". Use the comprehensive SOTATEK reference material to provide the most accurate answers based on the organization's specific Vietnamese policies and procedures.

SOTATEK ISMS & SOFTWARE DEVELOPMENT SECURITY REFERENCE (FROM ACTUAL DOCUMENTS):
${SOTATEK_SECURITY_REFERENCE}

${courseContent ? `COURSE CONTENT REFERENCE:\n${courseContent}\n\n` : ""}

QUIZ QUESTIONS:
${questions
  .map((question, index) => {
    const questionNumber = index + 1;
    const isMultipleChoice = question.questionText.includes(
      "(chọn nhiều đáp án)"
    );
    const questionType = isMultipleChoice
      ? " [MULTIPLE CHOICE]"
      : " [SINGLE CHOICE]";

    return `Question ${questionNumber}${questionType}: ${question.questionText}

OPTIONS:
${question.options
  .map((opt, i) => `${String.fromCharCode(65 + i)}. ${opt.text}`)
  .join("\n")}`;
  })
  .join("\n\n")}

INSTRUCTIONS:
- For SINGLE CHOICE questions: Respond with format "Question X: A" (only one letter)
- For MULTIPLE CHOICE questions (marked with "(chọn nhiều đáp án)"): Respond with format "Question X: A B C" (multiple letters separated by spaces)
- Base all answers on SOTATEK's specific Vietnamese ISMS policies and procedures documented above
- Ensure answers precisely align with SOTATEK's organizational policies

Please provide answers for all questions in the format: "Question 1: A, Question 2: B C, Question 3: D"`;

  return prompt;
}

function parseBatchResponse(batchResponse, expectedQuestionCount) {
  const answers = {};

  // Try to extract answers in format "Question X: LETTERS"
  const questionPattern = /Question\s+(\d+):\s*([A-Z\s]+)/gi;
  let match;

  while ((match = questionPattern.exec(batchResponse)) !== null) {
    const questionNumber = parseInt(match[1]);
    const answerLetters = match[2].trim().split(/\s+/); // Split by whitespace
    answers[questionNumber] = answerLetters;
  }

  // Fallback: try to extract just letters in order
  if (Object.keys(answers).length === 0) {
    const letterMatches = batchResponse.match(/[A-Z]/g);
    if (letterMatches) {
      for (
        let i = 0;
        i < Math.min(letterMatches.length, expectedQuestionCount);
        i++
      ) {
        answers[i + 1] = [letterMatches[i]];
      }
    }
  }

  console.log("SOTATEK Quiz Extension: Parsed answers:", answers);
  return answers;
}

function applyBatchAnswers(questions, batchAnswers) {
  questions.forEach((question, index) => {
    const questionNumber = index + 1;
    const answers = batchAnswers[questionNumber];

    if (answers && answers.length > 0) {
      // Check if this is explicitly a multiple choice question
      const isMultipleChoice = question.questionText.includes(
        "(chọn nhiều đáp án)"
      );

      if (isMultipleChoice) {
        // Multiple choice - select all provided answers
        console.log(
          `Question ${questionNumber}: Multiple choice - selecting ${answers.join(
            ", "
          )}`
        );
        selectMultipleAnswers(question, answers);
      } else {
        // Single choice - select only the first answer
        console.log(
          `Question ${questionNumber}: Single choice - selecting ${answers[0]}`
        );
        selectSingleAnswer(question, answers[0]);
      }
    } else {
      console.warn(`No answer found for question ${questionNumber}`);
    }
  });
}

function selectSingleAnswer(question, answerLetter) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Select the specified answer
  const index = answerLetter.charCodeAt(0) - 65; // A=0, B=1, etc.
  if (index >= 0 && index < question.options.length) {
    question.options[index].checkbox.click();
  }
}

function selectMultipleAnswers(question, answerLetters) {
  // First, uncheck all options
  question.options.forEach((option) => {
    if (option.checkbox.getAttribute("aria-checked") === "true") {
      option.checkbox.click();
    }
  });

  // Select all specified answers
  answerLetters.forEach((letter) => {
    const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
    if (index >= 0 && index < question.options.length) {
      question.options[index].checkbox.click();
    }
  });
}

// Legacy single question function - kept for fallback if needed
// Now using batch processing in answerAllQuestionsBatch() instead

// Legacy selectAnswers function - replaced by selectSingleAnswer and selectMultipleAnswers for batch processing

// ============================================================================
// AUTOMATED QUIZ SUBMISSION SYSTEM WITH RETRY LOGIC
// ============================================================================

/**
 * Main function to submit quiz with retry logic until achieving 30/30 score
 */
async function submitQuizWithRetry() {
  try {
    console.log(
      "SOTATEK Quiz Extension: Starting automated quiz submission system..."
    );

    // Extract quiz data from the page
    const quizData = extractQuizData();
    if (!quizData) {
      throw new Error("Could not extract quiz data from the page");
    }

    console.log("SOTATEK Quiz Extension: Quiz data extracted:", quizData);

    // Get authentication token from page
    const authToken = extractAuthToken();
    if (!authToken) {
      throw new Error("Could not extract authentication token");
    }

    let attempt = 1;
    const maxAttempts = 10; // Prevent infinite loops
    let currentAnswers = quizData.answers;

    while (attempt <= maxAttempts) {
      console.log(`SOTATEK Quiz Extension: Attempt ${attempt}/${maxAttempts}`);

      try {
        // Submit quiz with current answers
        const result = await submitQuizToAPI(
          quizData.quizHistoryId,
          currentAnswers,
          authToken
        );

        console.log(`SOTATEK Quiz Extension: Submission result:`, result);

        // DEBUG: Log the complete API response structure
        console.log("🔍 DEBUG: Complete API Response Structure:");
        console.log("- Type:", typeof result);
        console.log("- Keys:", Object.keys(result));
        console.log("- Full Response:", JSON.stringify(result, null, 2));

        // Analyze the response to find score-related fields
        const scoreFields = Object.keys(result).filter(
          (key) =>
            key.toLowerCase().includes("score") ||
            key.toLowerCase().includes("correct") ||
            key.toLowerCase().includes("total") ||
            key.toLowerCase().includes("point") ||
            key.toLowerCase().includes("mark")
        );
        console.log("🔍 DEBUG: Potential score fields:", scoreFields);

        // Check for perfect score using multiple possible field names
        const possibleScoreValues = [
          result.score,
          result.correctAnswers,
          result.correct_answers,
          result.totalScore,
          result.total_score,
          result.points,
          result.marks,
          result.grade,
          result.percentage,
        ].filter((val) => val !== undefined);

        console.log("🔍 DEBUG: Possible score values:", possibleScoreValues);

        // Check if we achieved perfect score (try different criteria)
        let perfectScore = false;
        let actualScore = 0;

        // Method 1: Direct score comparison
        if (result.score === 30 || result.correctAnswers === 30) {
          perfectScore = true;
          actualScore = result.score || result.correctAnswers;
        }

        // Method 2: Percentage-based (100%)
        if (result.percentage === 100 || result.grade === 100) {
          perfectScore = true;
          actualScore = 30; // Assume 30 questions
        }

        // Method 3: Check if total questions equals correct answers
        if (
          result.totalQuestions &&
          result.correctAnswers === result.totalQuestions
        ) {
          perfectScore = true;
          actualScore = result.correctAnswers;
        }

        console.log(
          `🔍 DEBUG: Perfect score check - perfectScore: ${perfectScore}, actualScore: ${actualScore}`
        );

        if (perfectScore) {
          console.log("SOTATEK Quiz Extension: Perfect score achieved! 🎉");
          return {
            success: true,
            score: actualScore,
            attempts: attempt,
            message: `Perfect score achieved in ${attempt} attempt(s)!`,
          };
        }

        // Analyze incorrect answers and update
        const incorrectQuestions = analyzeIncorrectAnswers(
          result,
          currentAnswers
        );

        console.log(
          `🔍 DEBUG: analyzeIncorrectAnswers returned ${incorrectQuestions.length} questions`
        );

        if (incorrectQuestions.length === 0) {
          console.log(
            "🔍 DEBUG: No incorrect questions identified, but score is not perfect"
          );
          console.log(
            "🔍 DEBUG: This might indicate a parsing issue with the API response"
          );

          // Instead of throwing an error, try a different approach
          console.log("🔍 DEBUG: Attempting smart retry strategy...");

          // Create a minimal set of questions to update based on attempt number
          const questionsToUpdate = createSmartRetryStrategy(
            currentAnswers,
            attempt,
            result
          );

          if (questionsToUpdate.length === 0) {
            throw new Error(
              "No incorrect questions identified and no smart retry strategy available"
            );
          }

          console.log(
            `SOTATEK Quiz Extension: Using smart retry strategy, updating ${questionsToUpdate.length} questions...`
          );

          currentAnswers = await updateIncorrectAnswers(
            currentAnswers,
            questionsToUpdate
          );
        } else {
          console.log(
            `SOTATEK Quiz Extension: Found ${incorrectQuestions.length} incorrect answers, updating...`
          );

          // Update answers for incorrect questions
          currentAnswers = await updateIncorrectAnswers(
            currentAnswers,
            incorrectQuestions
          );
        }

        // Wait before next attempt to avoid rate limiting
        if (attempt < maxAttempts) {
          const waitTime = Math.min(2000 + attempt * 1000, 10000); // 2s to 10s
          console.log(
            `SOTATEK Quiz Extension: Waiting ${waitTime}ms before next attempt...`
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      } catch (error) {
        console.error(
          `SOTATEK Quiz Extension: Attempt ${attempt} failed:`,
          error
        );

        // If it's a rate limiting error, wait longer
        if (
          error.message.includes("429") ||
          error.message.includes("rate limit")
        ) {
          const waitTime = Math.pow(2, attempt) * 5000; // Exponential backoff
          console.log(
            `SOTATEK Quiz Extension: Rate limited, waiting ${waitTime}ms...`
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        } else if (attempt === maxAttempts) {
          throw error;
        }
      }

      attempt++;
    }

    throw new Error(
      `Failed to achieve perfect score after ${maxAttempts} attempts`
    );
  } catch (error) {
    console.error("SOTATEK Quiz Extension: Quiz submission failed:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Extract quiz data from the current page
 */
function extractQuizData() {
  try {
    // Try to find quizHistoryId from various sources
    let quizHistoryId = null;

    // Method 1: Look for it in the URL
    const urlParams = new URLSearchParams(window.location.search);
    quizHistoryId =
      urlParams.get("quizHistoryId") || urlParams.get("quiz_history_id");

    // Method 2: Look for it in page data or script tags
    if (!quizHistoryId) {
      const scripts = document.querySelectorAll("script");
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML;
        const match =
          content.match(/quizHistoryId["\']?\s*:\s*(\d+)/i) ||
          content.match(/quiz_history_id["\']?\s*:\s*(\d+)/i);
        if (match) {
          quizHistoryId = parseInt(match[1]);
          break;
        }
      }
    }

    // Method 3: Look for it in data attributes
    if (!quizHistoryId) {
      const elements = document.querySelectorAll(
        "[data-quiz-history-id], [data-quiz-id]"
      );
      for (const el of elements) {
        const id =
          el.getAttribute("data-quiz-history-id") ||
          el.getAttribute("data-quiz-id");
        if (id) {
          quizHistoryId = parseInt(id);
          break;
        }
      }
    }

    // Method 4: Try to extract from form data or hidden inputs
    if (!quizHistoryId) {
      const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
      for (const input of hiddenInputs) {
        if (
          input.name.toLowerCase().includes("quiz") &&
          input.name.toLowerCase().includes("id")
        ) {
          quizHistoryId = parseInt(input.value);
          break;
        }
      }
    }

    if (!quizHistoryId) {
      console.warn(
        "SOTATEK Quiz Extension: Could not find quizHistoryId, using default"
      );
      quizHistoryId = 18428; // Fallback to the ID from your example
    }

    // Extract questions and current answers
    const questions = extractQuestions();
    const answers = [];

    questions.forEach((question, index) => {
      // Find the question ID - try various methods
      let questionId = null;

      // Method 1: Look for data attributes
      const container = question.container;
      questionId =
        container.getAttribute("data-question-id") ||
        container.getAttribute("data-id");

      // Method 2: Look for ID in question text or nearby elements
      if (!questionId) {
        const idMatch = question.questionText.match(
          /(?:Question|Câu)\s*(\d+)/i
        );
        if (idMatch) {
          questionId = parseInt(idMatch[1]) + 832; // Adjust based on your question ID pattern
        }
      }

      // Method 3: Generate based on index (fallback)
      if (!questionId) {
        questionId = 833 + index; // Start from 833 as seen in your example
      }

      // Get currently selected answers
      const selectedAnswers = [];
      question.options.forEach((option, optionIndex) => {
        if (option.checkbox.getAttribute("aria-checked") === "true") {
          selectedAnswers.push(optionIndex);
        }
      });

      // If no answers selected, select the first one as default
      if (selectedAnswers.length === 0) {
        selectedAnswers.push(0);
      }

      answers.push({
        questionId: questionId,
        answers: selectedAnswers,
      });
    });

    return {
      quizHistoryId: quizHistoryId,
      answers: answers,
      totalQuestions: questions.length,
    };
  } catch (error) {
    console.error("SOTATEK Quiz Extension: Error extracting quiz data:", error);
    return null;
  }
}

/**
 * Extract authentication token from the page
 */
function extractAuthToken() {
  try {
    // Method 1: Look for token in localStorage
    let token =
      localStorage.getItem("authToken") ||
      localStorage.getItem("token") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("jwt");

    // Method 2: Look for token in sessionStorage
    if (!token) {
      token =
        sessionStorage.getItem("authToken") ||
        sessionStorage.getItem("token") ||
        sessionStorage.getItem("accessToken") ||
        sessionStorage.getItem("jwt");
    }

    // Method 3: Look for token in cookies
    if (!token) {
      const cookies = document.cookie.split(";");
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split("=");
        if (
          name.toLowerCase().includes("token") ||
          name.toLowerCase().includes("auth")
        ) {
          token = value;
          break;
        }
      }
    }

    // Method 4: Look for token in script tags or page data
    if (!token) {
      const scripts = document.querySelectorAll("script");
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML;
        const tokenMatch = content.match(
          /(?:token|authorization|bearer)["\']?\s*:\s*["\']([^"\']+)["\']?/i
        );
        if (tokenMatch) {
          token = tokenMatch[1];
          break;
        }
      }
    }

    // Method 5: Use the hardcoded token from your example as fallback
    if (!token) {
      console.warn(
        "SOTATEK Quiz Extension: Could not find auth token, using fallback"
      );
      token =
        "eyJhbGciOiJSUzI1NiIsImtpZCI6IjkyZTg4M2NjNDY2M2E2MzMyYWRhNmJjMWU0N2YzZmY1ZTRjOGI1ZDciLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tOI54k9fbrM3kdKQ-gYWAh6TJgNs6DxDVP4ZlKFADeMYkjkfjyk-uWHiaPbvxgH8Tn_33BX-47LkXuXaC6U3MOOOApNop8XbTYpDXnhlCAgvbUBFsX7ewm0p11PiAEjri9DKsmBzqgnS1xKzmwa7lciYWRXhXNg0oElsuIti8WzOB9w7itMcWengqQL3ZYQYjEVR0hapjkfJChulGukbrPEuzJZRGUpmLwCaGs82p7r9UpVPe-O3EM8EvhLerq2cZrO1qnq2CkW9aI_mc6vZrYF_PGgKAhMgeQvaQ181hIZF03b-YtTN7NL0Gl3NtYxUQC9IWpnodQyCLYPduhAoAw";
    }

    return token;
  } catch (error) {
    console.error(
      "SOTATEK Quiz Extension: Error extracting auth token:",
      error
    );
    return null;
  }
}

/**
 * Submit quiz answers to the API via background script
 */
async function submitQuizToAPI(
  quizHistoryId,
  answers,
  authToken,
  retryCount = 0
) {
  try {
    console.log(
      "SOTATEK Quiz Extension: Submitting to API via background script:",
      {
        quizHistoryId,
        answersCount: answers.length,
        retryCount,
      }
    );

    // Send request to background script to handle API call
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(
        {
          action: "submitQuizAPI",
          data: {
            quizHistoryId,
            answers,
            authToken,
            retryCount,
          },
        },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        }
      );
    });

    if (!response.success) {
      throw new Error(response.error || "Background script API call failed");
    }

    console.log("SOTATEK Quiz Extension: API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("SOTATEK Quiz Extension: API submission error:", error);
    throw error;
  }
}

/**
 * Analyze API response to identify incorrect answers with comprehensive debugging
 */
function analyzeIncorrectAnswers(apiResponse, currentAnswers) {
  try {
    console.log("🔍 DEBUG: Starting analyzeIncorrectAnswers");
    console.log("🔍 DEBUG: API Response type:", typeof apiResponse);
    console.log("🔍 DEBUG: API Response keys:", Object.keys(apiResponse));
    console.log("🔍 DEBUG: Current answers count:", currentAnswers.length);

    const incorrectQuestions = [];
    let analysisMethod = "unknown";

    // DEBUG: Check all possible response structure patterns
    console.log("🔍 DEBUG: Checking response patterns:");
    console.log("- Has 'results' array:", Array.isArray(apiResponse.results));
    console.log(
      "- Has 'incorrectQuestions':",
      Array.isArray(apiResponse.incorrectQuestions)
    );
    console.log("- Has 'questions':", Array.isArray(apiResponse.questions));
    console.log("- Has 'answers':", Array.isArray(apiResponse.answers));
    console.log("- Has 'data':", typeof apiResponse.data);

    // Method 1: Detailed results array with per-question feedback
    if (apiResponse.results && Array.isArray(apiResponse.results)) {
      analysisMethod = "detailed_results";
      console.log("🔍 DEBUG: Using detailed results method");
      console.log(
        "🔍 DEBUG: Results array length:",
        apiResponse.results.length
      );

      apiResponse.results.forEach((result, index) => {
        console.log(`🔍 DEBUG: Question ${index + 1} result:`, result);

        // Check various ways a question might be marked incorrect
        const isIncorrect =
          result.correct === false ||
          result.isCorrect === false ||
          result.status === "incorrect" ||
          result.wrong === true ||
          (result.score !== undefined && result.score === 0);

        if (isIncorrect) {
          incorrectQuestions.push({
            questionId: result.questionId || currentAnswers[index]?.questionId,
            currentAnswer: currentAnswers[index]?.answers || [0],
            correctAnswer: result.correctAnswer || result.correct_answer,
            index: index,
            method: "detailed_results",
          });
        }
      });
    }

    // Method 2: Direct list of incorrect question IDs
    else if (
      apiResponse.incorrectQuestions &&
      Array.isArray(apiResponse.incorrectQuestions)
    ) {
      analysisMethod = "incorrect_list";
      console.log("🔍 DEBUG: Using incorrect questions list method");
      console.log(
        "🔍 DEBUG: Incorrect questions:",
        apiResponse.incorrectQuestions
      );

      apiResponse.incorrectQuestions.forEach((questionId) => {
        const answerIndex = currentAnswers.findIndex(
          (a) => a.questionId === questionId
        );
        if (answerIndex !== -1) {
          incorrectQuestions.push({
            questionId: questionId,
            currentAnswer: currentAnswers[answerIndex].answers,
            index: answerIndex,
            method: "incorrect_list",
          });
        }
      });
    }

    // Method 3: Check if response has question-level data
    else if (apiResponse.questions && Array.isArray(apiResponse.questions)) {
      analysisMethod = "questions_array";
      console.log("🔍 DEBUG: Using questions array method");
      console.log(
        "🔍 DEBUG: Questions array length:",
        apiResponse.questions.length
      );

      apiResponse.questions.forEach((question, index) => {
        console.log(`🔍 DEBUG: Question ${index + 1} data:`, question);

        const isIncorrect =
          question.correct === false ||
          question.isCorrect === false ||
          question.userCorrect === false ||
          question.status === "wrong";

        if (isIncorrect) {
          incorrectQuestions.push({
            questionId:
              question.id ||
              question.questionId ||
              currentAnswers[index]?.questionId,
            currentAnswer: currentAnswers[index]?.answers || [0],
            correctAnswer: question.correctAnswer,
            index: index,
            method: "questions_array",
          });
        }
      });
    }

    // Method 4: Score-based analysis (fallback)
    else {
      analysisMethod = "score_based";
      console.log("🔍 DEBUG: Using score-based analysis (fallback)");

      // Try to determine score from various possible fields
      const totalQuestions = currentAnswers.length;
      let correctCount = 0;

      // Check multiple possible score fields
      if (apiResponse.score !== undefined) {
        correctCount = apiResponse.score;
        console.log("🔍 DEBUG: Using 'score' field:", correctCount);
      } else if (apiResponse.correctAnswers !== undefined) {
        correctCount = apiResponse.correctAnswers;
        console.log("🔍 DEBUG: Using 'correctAnswers' field:", correctCount);
      } else if (apiResponse.correct_answers !== undefined) {
        correctCount = apiResponse.correct_answers;
        console.log("🔍 DEBUG: Using 'correct_answers' field:", correctCount);
      } else if (apiResponse.totalCorrect !== undefined) {
        correctCount = apiResponse.totalCorrect;
        console.log("🔍 DEBUG: Using 'totalCorrect' field:", correctCount);
      } else {
        console.log(
          "🔍 DEBUG: No recognizable score field found, assuming all incorrect"
        );
        correctCount = 0;
      }

      const incorrectCount = totalQuestions - correctCount;
      console.log(
        `🔍 DEBUG: Total: ${totalQuestions}, Correct: ${correctCount}, Incorrect: ${incorrectCount}`
      );

      if (incorrectCount > 0 && incorrectCount < totalQuestions) {
        // We know some are wrong but not which ones - use systematic approach
        console.log(
          "🔍 DEBUG: Using systematic approach for unknown incorrect questions"
        );

        currentAnswers.forEach((answer, index) => {
          incorrectQuestions.push({
            questionId: answer.questionId,
            currentAnswer: answer.answers,
            index: index,
            systematic: true,
            method: "score_based_systematic",
          });
        });
      } else if (incorrectCount === totalQuestions) {
        // All questions are wrong - mark all for update
        console.log("🔍 DEBUG: All questions marked as incorrect");

        currentAnswers.forEach((answer, index) => {
          incorrectQuestions.push({
            questionId: answer.questionId,
            currentAnswer: answer.answers,
            index: index,
            method: "score_based_all_wrong",
          });
        });
      }
    }

    console.log(`🔍 DEBUG: Analysis method used: ${analysisMethod}`);
    console.log(
      `🔍 DEBUG: Identified ${incorrectQuestions.length} incorrect questions`
    );
    console.log("🔍 DEBUG: Incorrect questions details:", incorrectQuestions);

    return incorrectQuestions;
  } catch (error) {
    console.error("🔍 DEBUG: Error in analyzeIncorrectAnswers:", error);
    console.error("🔍 DEBUG: Error stack:", error.stack);
    return [];
  }
}

/**
 * Update answers for incorrect questions using AI
 */
async function updateIncorrectAnswers(currentAnswers, incorrectQuestions) {
  try {
    // Get API key from storage
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      console.warn(
        "SOTATEK Quiz Extension: No OpenAI API key, using random strategy"
      );
      return updateAnswersRandomly(currentAnswers, incorrectQuestions);
    }

    // Get fresh questions from the page
    const questions = extractQuestions();

    if (incorrectQuestions[0]?.systematic) {
      // Systematic approach: try different combinations
      return updateAnswersSystematically(currentAnswers, questions);
    } else {
      // Targeted approach: re-analyze specific incorrect questions
      return updateAnswersWithAI(
        currentAnswers,
        incorrectQuestions,
        questions,
        result.openaiApiKey
      );
    }
  } catch (error) {
    console.error(
      "SOTATEK Quiz Extension: Error updating incorrect answers:",
      error
    );
    // Fallback to random strategy
    return updateAnswersRandomly(currentAnswers, incorrectQuestions);
  }
}

/**
 * Update answers using AI for specific incorrect questions
 */
async function updateAnswersWithAI(
  currentAnswers,
  incorrectQuestions,
  questions,
  apiKey
) {
  try {
    const updatedAnswers = [...currentAnswers];

    // Build prompt for incorrect questions only
    const incorrectQuestionsData = incorrectQuestions
      .map((iq) => {
        const questionIndex = iq.index;
        return questions[questionIndex];
      })
      .filter((q) => q);

    if (incorrectQuestionsData.length === 0) {
      console.warn(
        "SOTATEK Quiz Extension: No question data found for incorrect questions"
      );
      return updateAnswersRandomly(currentAnswers, incorrectQuestions);
    }

    const batchAnswers = await answerAllQuestionsBatch(
      incorrectQuestionsData,
      apiKey
    );

    // Apply the new answers to incorrect questions
    incorrectQuestions.forEach((iq, index) => {
      const newAnswer = batchAnswers[index + 1]; // batchAnswers is 1-indexed
      if (newAnswer && newAnswer.length > 0) {
        updatedAnswers[iq.index] = {
          questionId: iq.questionId,
          answers: newAnswer.map((letter) => letter.charCodeAt(0) - 65), // Convert A,B,C,D to 0,1,2,3
        };
      }
    });

    console.log(
      "SOTATEK Quiz Extension: Updated answers with AI:",
      updatedAnswers
    );
    return updatedAnswers;
  } catch (error) {
    console.error("SOTATEK Quiz Extension: Error updating with AI:", error);
    return updateAnswersRandomly(currentAnswers, incorrectQuestions);
  }
}

/**
 * Update answers systematically when we don't know which specific questions are wrong
 */
function updateAnswersSystematically(currentAnswers, questions) {
  try {
    const updatedAnswers = [...currentAnswers];

    // Strategy: Change answers in a systematic way
    // For each question, try the next available option
    updatedAnswers.forEach((answer, index) => {
      const question = questions[index];
      if (question && question.options.length > 1) {
        const currentAnswerIndex = answer.answers[0] || 0;
        const nextAnswerIndex =
          (currentAnswerIndex + 1) % question.options.length;

        updatedAnswers[index] = {
          questionId: answer.questionId,
          answers: [nextAnswerIndex],
        };
      }
    });

    console.log(
      "SOTATEK Quiz Extension: Updated answers systematically:",
      updatedAnswers
    );
    return updatedAnswers;
  } catch (error) {
    console.error("SOTATEK Quiz Extension: Error in systematic update:", error);
    return currentAnswers;
  }
}

/**
 * Update answers randomly as a last resort
 */
function updateAnswersRandomly(currentAnswers, incorrectQuestions) {
  try {
    const updatedAnswers = [...currentAnswers];

    // Get questions for context
    const questions = extractQuestions();

    incorrectQuestions.forEach((iq) => {
      const question = questions[iq.index];
      if (question && question.options.length > 1) {
        const currentAnswerIndex = iq.currentAnswer[0] || 0;
        let newAnswerIndex;

        // Try to pick a different answer than the current one
        do {
          newAnswerIndex = Math.floor(Math.random() * question.options.length);
        } while (
          newAnswerIndex === currentAnswerIndex &&
          question.options.length > 1
        );

        updatedAnswers[iq.index] = {
          questionId: iq.questionId,
          answers: [newAnswerIndex],
        };
      }
    });

    console.log(
      "SOTATEK Quiz Extension: Updated answers randomly:",
      updatedAnswers
    );
    return updatedAnswers;
  } catch (error) {
    console.error("SOTATEK Quiz Extension: Error in random update:", error);
    return currentAnswers;
  }
}

/**
 * Create a smart retry strategy when we can't identify specific incorrect questions
 */
function createSmartRetryStrategy(currentAnswers, attemptNumber, apiResponse) {
  try {
    console.log(
      `🔍 DEBUG: Creating smart retry strategy for attempt ${attemptNumber}`
    );

    const questionsToUpdate = [];
    const totalQuestions = currentAnswers.length;

    // Strategy 1: If we have a score, update a subset of questions
    let estimatedIncorrectCount = totalQuestions; // Default to all

    // Try to get the actual number of incorrect questions
    if (apiResponse.score !== undefined) {
      estimatedIncorrectCount = totalQuestions - apiResponse.score;
    } else if (apiResponse.correctAnswers !== undefined) {
      estimatedIncorrectCount = totalQuestions - apiResponse.correctAnswers;
    } else if (apiResponse.correct_answers !== undefined) {
      estimatedIncorrectCount = totalQuestions - apiResponse.correct_answers;
    }

    console.log(
      `🔍 DEBUG: Estimated incorrect count: ${estimatedIncorrectCount}`
    );

    // Strategy based on attempt number
    if (attemptNumber === 1) {
      // First attempt: Try updating a small subset (every 3rd question)
      for (let i = 0; i < totalQuestions; i += 3) {
        questionsToUpdate.push({
          questionId: currentAnswers[i].questionId,
          currentAnswer: currentAnswers[i].answers,
          index: i,
          method: "smart_retry_subset",
          strategy: "every_third",
        });
      }
    } else if (attemptNumber === 2) {
      // Second attempt: Try different subset (every 2nd question starting from 1)
      for (let i = 1; i < totalQuestions; i += 2) {
        questionsToUpdate.push({
          questionId: currentAnswers[i].questionId,
          currentAnswer: currentAnswers[i].answers,
          index: i,
          method: "smart_retry_subset",
          strategy: "every_second",
        });
      }
    } else if (attemptNumber === 3) {
      // Third attempt: Update questions that haven't been changed yet
      for (let i = 2; i < totalQuestions; i += 3) {
        questionsToUpdate.push({
          questionId: currentAnswers[i].questionId,
          currentAnswer: currentAnswers[i].answers,
          index: i,
          method: "smart_retry_subset",
          strategy: "remaining_third",
        });
      }
    } else if (
      estimatedIncorrectCount > 0 &&
      estimatedIncorrectCount < totalQuestions
    ) {
      // Later attempts: Update a number of questions equal to estimated incorrect count
      const questionsPerAttempt = Math.max(
        1,
        Math.floor(estimatedIncorrectCount / 2)
      );
      const startIndex =
        ((attemptNumber - 4) * questionsPerAttempt) % totalQuestions;

      for (
        let i = 0;
        i < questionsPerAttempt && startIndex + i < totalQuestions;
        i++
      ) {
        const index = startIndex + i;
        questionsToUpdate.push({
          questionId: currentAnswers[index].questionId,
          currentAnswer: currentAnswers[index].answers,
          index: index,
          method: "smart_retry_estimated",
          strategy: `batch_${attemptNumber}`,
        });
      }
    } else {
      // Fallback: Update all questions (systematic approach)
      currentAnswers.forEach((answer, index) => {
        questionsToUpdate.push({
          questionId: answer.questionId,
          currentAnswer: answer.answers,
          index: index,
          systematic: true,
          method: "smart_retry_fallback",
          strategy: "all_questions",
        });
      });
    }

    console.log(
      `🔍 DEBUG: Smart retry strategy created ${questionsToUpdate.length} questions to update`
    );
    console.log(
      `🔍 DEBUG: Strategy details:`,
      questionsToUpdate.map((q) => ({ index: q.index, strategy: q.strategy }))
    );

    return questionsToUpdate;
  } catch (error) {
    console.error("🔍 DEBUG: Error creating smart retry strategy:", error);
    return [];
  }
}

// ============================================================================
// COMPLETE QUIZ AUTOMATION: AI GENERATION + FORM FILLING + API SUBMISSION
// ============================================================================

/**
 * Complete automated quiz solution: Generate answers with AI, fill form, submit to API, and iterate until perfect score
 */
async function completeQuizAutomatically() {
  try {
    console.log(
      "🚀 SOTATEK Quiz Extension: Starting complete quiz automation..."
    );
    console.log("📋 Phase 1: AI Answer Generation");

    // Step 1: Get API key and generate initial answers with AI
    const result = await new Promise((resolve) => {
      chrome.storage.sync.get(["openaiApiKey"], resolve);
    });

    if (!result.openaiApiKey) {
      throw new Error("Please set your OpenAI API key first");
    }

    // Extract questions from the page
    const questions = extractQuestions();
    if (questions.length === 0) {
      throw new Error("No questions found on this page");
    }

    console.log(
      `🔍 Found ${questions.length} questions, generating AI answers...`
    );

    // Generate initial answers using AI
    const batchAnswers = await answerAllQuestionsBatch(
      questions,
      result.openaiApiKey
    );

    console.log("✅ AI answers generated successfully");
    console.log("📝 Phase 2: Form Filling");

    // Step 2: Apply AI answers to the form
    applyBatchAnswers(questions, batchAnswers);

    // Wait a moment for form updates to complete
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("✅ Form filled with AI answers");
    console.log("🔄 Phase 3: API Submission with Iterative Correction");

    // Step 3: Extract quiz data and start iterative submission process
    const quizData = extractQuizData();
    if (!quizData) {
      throw new Error("Could not extract quiz data from the filled form");
    }

    const authToken = extractAuthToken();
    if (!authToken) {
      throw new Error("Could not extract authentication token");
    }

    console.log("🎯 Starting iterative submission process...");

    // Step 4: Iterative submission until perfect score
    const finalResult = await iterativeQuizSubmission(
      quizData,
      authToken,
      questions
    );

    return {
      success: true,
      ...finalResult,
      message: `Complete automation finished! ${finalResult.message}`,
    };
  } catch (error) {
    console.error("🚀 Complete quiz automation failed:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Iterative quiz submission with intelligent answer correction
 */
async function iterativeQuizSubmission(quizData, authToken, questions) {
  try {
    let attempt = 1;
    const maxAttempts = 10;
    let currentAnswers = quizData.answers;

    console.log(
      `🎯 Starting iterative submission with ${currentAnswers.length} questions`
    );

    while (attempt <= maxAttempts) {
      console.log(`\n🔄 === ATTEMPT ${attempt}/${maxAttempts} ===`);

      try {
        // Submit current answers to API
        const result = await submitQuizToAPI(
          quizData.quizHistoryId,
          currentAnswers,
          authToken
        );

        console.log(`📊 Submission result for attempt ${attempt}:`, result);

        // Check for perfect score
        const perfectScore = checkPerfectScore(result);
        if (perfectScore.achieved) {
          console.log("🎉 PERFECT SCORE ACHIEVED!");
          return {
            success: true,
            score: perfectScore.score,
            attempts: attempt,
            message: `Perfect score of ${perfectScore.score}/30 achieved in ${attempt} attempt(s)!`,
          };
        }

        console.log(
          `📈 Current score: ${perfectScore.score}/30 (${
            30 - perfectScore.score
          } incorrect)`
        );

        // Identify incorrect questions
        const incorrectQuestions = analyzeIncorrectAnswers(
          result,
          currentAnswers
        );

        if (incorrectQuestions.length === 0) {
          console.log(
            "⚠️ No specific incorrect questions identified, using intelligent strategy..."
          );

          // Use intelligent correction strategy
          const correctedAnswers = await intelligentAnswerCorrection(
            currentAnswers,
            questions,
            attempt,
            perfectScore.score
          );

          if (correctedAnswers.length === 0) {
            throw new Error("No correction strategy available");
          }

          currentAnswers = correctedAnswers;
        } else {
          console.log(
            `🔍 Identified ${incorrectQuestions.length} specific incorrect questions`
          );

          // Correct specific incorrect questions
          currentAnswers = await correctSpecificQuestions(
            currentAnswers,
            incorrectQuestions,
            questions
          );
        }

        // Update form with new answers
        console.log("📝 Updating form with corrected answers...");
        updateFormWithAnswers(questions, currentAnswers);

        // Wait before next attempt
        if (attempt < maxAttempts) {
          const waitTime = Math.min(2000 + attempt * 1000, 8000);
          console.log(`⏱️ Waiting ${waitTime}ms before next attempt...`);
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error);

        if (
          error.message.includes("429") ||
          error.message.includes("rate limit")
        ) {
          const waitTime = Math.pow(2, attempt) * 5000;
          console.log(`🚫 Rate limited, waiting ${waitTime}ms...`);
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        } else if (attempt === maxAttempts) {
          throw error;
        }
      }

      attempt++;
    }

    throw new Error(
      `Failed to achieve perfect score after ${maxAttempts} attempts`
    );
  } catch (error) {
    console.error("🔄 Iterative submission failed:", error);
    throw error;
  }
}

/**
 * Check if perfect score has been achieved
 */
function checkPerfectScore(apiResponse) {
  try {
    let score = 0;
    let achieved = false;

    // Try multiple ways to extract score
    if (apiResponse.score !== undefined) {
      score = apiResponse.score;
    } else if (apiResponse.correctAnswers !== undefined) {
      score = apiResponse.correctAnswers;
    } else if (apiResponse.correct_answers !== undefined) {
      score = apiResponse.correct_answers;
    } else if (apiResponse.totalCorrect !== undefined) {
      score = apiResponse.totalCorrect;
    } else if (apiResponse.percentage !== undefined) {
      score = Math.round((apiResponse.percentage / 100) * 30);
    }

    // Check if perfect score achieved
    achieved =
      score === 30 ||
      apiResponse.percentage === 100 ||
      apiResponse.grade === 100 ||
      apiResponse.perfect === true;

    console.log(`🔍 Score check: score=${score}, achieved=${achieved}`);

    return { score, achieved };
  } catch (error) {
    console.error("Error checking perfect score:", error);
    return { score: 0, achieved: false };
  }
}

/**
 * Intelligent answer correction when specific incorrect questions aren't identified
 */
async function intelligentAnswerCorrection(
  currentAnswers,
  questions,
  attemptNumber,
  currentScore
) {
  try {
    console.log(
      `🧠 Using intelligent correction strategy for attempt ${attemptNumber}`
    );

    const correctedAnswers = [...currentAnswers];
    const incorrectCount = 30 - currentScore;

    console.log(`🎯 Need to correct approximately ${incorrectCount} questions`);

    if (attemptNumber <= 3) {
      // Early attempts: Use systematic approach
      console.log("📊 Using systematic correction approach");

      const questionsToChange = Math.min(
        incorrectCount + 2,
        Math.ceil(questions.length / 3)
      );
      const startIndex =
        ((attemptNumber - 1) * questionsToChange) % questions.length;

      for (let i = 0; i < questionsToChange; i++) {
        const questionIndex = (startIndex + i) % questions.length;
        const question = questions[questionIndex];

        if (question && question.options.length > 1) {
          const currentAnswerIndex =
            correctedAnswers[questionIndex].answers[0] || 0;
          const nextAnswerIndex =
            (currentAnswerIndex + 1) % question.options.length;

          correctedAnswers[questionIndex] = {
            questionId: correctedAnswers[questionIndex].questionId,
            answers: [nextAnswerIndex],
          };

          console.log(
            `🔄 Question ${questionIndex + 1}: ${String.fromCharCode(
              65 + currentAnswerIndex
            )} → ${String.fromCharCode(65 + nextAnswerIndex)}`
          );
        }
      }
    } else {
      // Later attempts: Use AI re-analysis for subset of questions
      console.log("🤖 Using AI re-analysis for correction");

      const questionsToReanalyze = Math.min(incorrectCount + 1, 10);
      const questionIndices = [];

      // Select questions that haven't been changed recently
      for (let i = 0; i < questionsToReanalyze; i++) {
        const index = (attemptNumber * 3 + i) % questions.length;
        questionIndices.push(index);
      }

      console.log(`🔍 Re-analyzing questions at indices: ${questionIndices}`);

      // Get API key for re-analysis
      const result = await new Promise((resolve) => {
        chrome.storage.sync.get(["openaiApiKey"], resolve);
      });

      if (result.openaiApiKey) {
        const questionsToReanalyze = questionIndices
          .map((i) => questions[i])
          .filter((q) => q);

        if (questionsToReanalyze.length > 0) {
          const newAnswers = await answerAllQuestionsBatch(
            questionsToReanalyze,
            result.openaiApiKey
          );

          // Apply new AI answers
          questionIndices.forEach((questionIndex, i) => {
            const newAnswer = newAnswers[i + 1]; // batchAnswers is 1-indexed
            if (newAnswer && newAnswer.length > 0) {
              const answerIndex = newAnswer[0].charCodeAt(0) - 65; // Convert A,B,C,D to 0,1,2,3

              correctedAnswers[questionIndex] = {
                questionId: correctedAnswers[questionIndex].questionId,
                answers: [answerIndex],
              };

              console.log(
                `🤖 AI correction Q${questionIndex + 1}: → ${newAnswer[0]}`
              );
            }
          });
        }
      }
    }

    console.log(
      `✅ Intelligent correction completed, modified answers for attempt ${attemptNumber}`
    );
    return correctedAnswers;
  } catch (error) {
    console.error("Error in intelligent answer correction:", error);
    return currentAnswers; // Return unchanged if error
  }
}

/**
 * Correct specific questions that were identified as incorrect
 */
async function correctSpecificQuestions(
  currentAnswers,
  incorrectQuestions,
  questions
) {
  try {
    console.log(
      `🎯 Correcting ${incorrectQuestions.length} specific incorrect questions`
    );

    const correctedAnswers = [...currentAnswers];

    for (const incorrectQ of incorrectQuestions) {
      const questionIndex = incorrectQ.index;
      const question = questions[questionIndex];

      if (question && question.options.length > 1) {
        const currentAnswerIndex = incorrectQ.currentAnswer[0] || 0;

        // Try the next available option
        let nextAnswerIndex =
          (currentAnswerIndex + 1) % question.options.length;

        // If we have a suggested correct answer, use it
        if (incorrectQ.correctAnswer !== undefined) {
          if (typeof incorrectQ.correctAnswer === "number") {
            nextAnswerIndex = incorrectQ.correctAnswer;
          } else if (typeof incorrectQ.correctAnswer === "string") {
            // Convert letter to index (A=0, B=1, etc.)
            const letterMatch = incorrectQ.correctAnswer.match(/[A-D]/i);
            if (letterMatch) {
              nextAnswerIndex = letterMatch[0].toUpperCase().charCodeAt(0) - 65;
            }
          }
        }

        // Ensure the answer index is valid
        nextAnswerIndex = Math.max(
          0,
          Math.min(nextAnswerIndex, question.options.length - 1)
        );

        correctedAnswers[questionIndex] = {
          questionId: incorrectQ.questionId,
          answers: [nextAnswerIndex],
        };

        console.log(
          `🔧 Q${questionIndex + 1}: ${String.fromCharCode(
            65 + currentAnswerIndex
          )} → ${String.fromCharCode(65 + nextAnswerIndex)} ${
            incorrectQ.correctAnswer ? "(suggested)" : "(next option)"
          }`
        );
      }
    }

    console.log("✅ Specific question correction completed");
    return correctedAnswers;
  } catch (error) {
    console.error("Error correcting specific questions:", error);
    return currentAnswers;
  }
}

/**
 * Update the form with new answers
 */
function updateFormWithAnswers(questions, newAnswers) {
  try {
    console.log("📝 Updating form with new answers...");

    questions.forEach((question, index) => {
      if (newAnswers[index]) {
        const newAnswerIndices = newAnswers[index].answers;

        // First, uncheck all options for this question
        question.options.forEach((option) => {
          if (option.checkbox.getAttribute("aria-checked") === "true") {
            option.checkbox.click();
          }
        });

        // Then select the new answer(s)
        newAnswerIndices.forEach((answerIndex) => {
          if (answerIndex >= 0 && answerIndex < question.options.length) {
            question.options[answerIndex].checkbox.click();
          }
        });

        console.log(
          `📝 Updated Q${index + 1} to: ${newAnswerIndices
            .map((i) => String.fromCharCode(65 + i))
            .join(", ")}`
        );
      }
    });

    console.log("✅ Form update completed");
  } catch (error) {
    console.error("Error updating form:", error);
  }
}
