// Test script for the automated quiz submission system
// Run this in the browser console on a SotaTek LMS quiz page

console.log("🧪 Testing Automated Quiz Submission System");

// Test 1: Quiz Data Extraction
console.log("\n📊 Test 1: Quiz Data Extraction");
try {
  const quizData = extractQuizData();
  console.log("✅ Quiz data extracted successfully:", quizData);
  
  if (!quizData) {
    console.error("❌ Quiz data extraction failed");
  } else {
    console.log(`📝 Found ${quizData.totalQuestions} questions`);
    console.log(`🆔 Quiz History ID: ${quizData.quizHistoryId}`);
    console.log(`📋 Sample answers:`, quizData.answers.slice(0, 3));
  }
} catch (error) {
  console.error("❌ Quiz data extraction error:", error);
}

// Test 2: Authentication Token Extraction
console.log("\n🔐 Test 2: Authentication Token Extraction");
try {
  const authToken = extractAuthToken();
  console.log("✅ Auth token extracted successfully");
  
  if (!authToken) {
    console.error("❌ Auth token extraction failed");
  } else {
    console.log(`🎫 Token length: ${authToken.length} characters`);
    console.log(`🎫 Token preview: ${authToken.substring(0, 50)}...`);
  }
} catch (error) {
  console.error("❌ Auth token extraction error:", error);
}

// Test 3: Question Extraction
console.log("\n❓ Test 3: Question Extraction");
try {
  const questions = extractQuestions();
  console.log("✅ Questions extracted successfully");
  
  if (questions.length === 0) {
    console.error("❌ No questions found on page");
  } else {
    console.log(`📝 Found ${questions.length} questions`);
    console.log("📋 Sample question:", {
      text: questions[0].questionText.substring(0, 100) + "...",
      optionsCount: questions[0].options.length
    });
  }
} catch (error) {
  console.error("❌ Question extraction error:", error);
}

// Test 4: Mock API Response Analysis
console.log("\n🔍 Test 4: API Response Analysis");
try {
  // Mock API responses to test different formats
  const mockResponses = [
    { score: 25, totalQuestions: 30 },
    { correctAnswers: 28 },
    { 
      results: [
        { questionId: 849, correct: true },
        { questionId: 861, correct: false },
        { questionId: 855, correct: true }
      ]
    },
    { incorrectQuestions: [861, 852, 860] }
  ];
  
  const mockAnswers = [
    { questionId: 849, answers: [2] },
    { questionId: 861, answers: [0] },
    { questionId: 855, answers: [2] }
  ];
  
  mockResponses.forEach((response, index) => {
    console.log(`\n🧪 Testing response format ${index + 1}:`, response);
    const incorrectQuestions = analyzeIncorrectAnswers(response, mockAnswers);
    console.log(`📊 Identified ${incorrectQuestions.length} incorrect questions:`, incorrectQuestions);
  });
  
  console.log("✅ API response analysis tests completed");
} catch (error) {
  console.error("❌ API response analysis error:", error);
}

// Test 5: Answer Update Strategies
console.log("\n🔄 Test 5: Answer Update Strategies");
try {
  const mockCurrentAnswers = [
    { questionId: 849, answers: [0] },
    { questionId: 861, answers: [1] },
    { questionId: 855, answers: [2] }
  ];
  
  const mockIncorrectQuestions = [
    { questionId: 861, currentAnswer: [1], index: 1 },
    { questionId: 855, currentAnswer: [2], index: 2 }
  ];
  
  // Test systematic update
  console.log("\n🔄 Testing systematic update:");
  const questions = extractQuestions();
  if (questions.length > 0) {
    const systematicUpdate = updateAnswersSystematically(mockCurrentAnswers, questions.slice(0, 3));
    console.log("✅ Systematic update result:", systematicUpdate);
  }
  
  // Test random update
  console.log("\n🎲 Testing random update:");
  const randomUpdate = updateAnswersRandomly(mockCurrentAnswers, mockIncorrectQuestions);
  console.log("✅ Random update result:", randomUpdate);
  
} catch (error) {
  console.error("❌ Answer update strategy error:", error);
}

// Test 6: Full System Integration Test (Dry Run)
console.log("\n🚀 Test 6: Full System Integration (Dry Run)");
try {
  console.log("🔍 Checking all system components...");
  
  // Check if all required functions exist
  const requiredFunctions = [
    'extractQuizData',
    'extractAuthToken',
    'extractQuestions',
    'analyzeIncorrectAnswers',
    'updateAnswersSystematically',
    'updateAnswersRandomly'
  ];
  
  const missingFunctions = requiredFunctions.filter(func => typeof window[func] !== 'function');
  
  if (missingFunctions.length > 0) {
    console.error("❌ Missing functions:", missingFunctions);
  } else {
    console.log("✅ All required functions are available");
  }
  
  // Check if we can extract basic data
  const hasQuizData = extractQuizData() !== null;
  const hasAuthToken = extractAuthToken() !== null;
  const hasQuestions = extractQuestions().length > 0;
  
  console.log("📊 System readiness check:");
  console.log(`  Quiz Data: ${hasQuizData ? '✅' : '❌'}`);
  console.log(`  Auth Token: ${hasAuthToken ? '✅' : '❌'}`);
  console.log(`  Questions: ${hasQuestions ? '✅' : '❌'}`);
  
  if (hasQuizData && hasAuthToken && hasQuestions) {
    console.log("🎉 System is ready for automated quiz submission!");
    console.log("💡 To start the automated submission, click the 'Submit Quiz with Retry' button in the extension popup.");
  } else {
    console.log("⚠️ System is not fully ready. Please check the failed components above.");
  }
  
} catch (error) {
  console.error("❌ System integration test error:", error);
}

console.log("\n🏁 Test suite completed!");
console.log("📝 Check the results above to ensure all components are working correctly.");
console.log("🚀 If all tests pass, the automated quiz submission system is ready to use!");
